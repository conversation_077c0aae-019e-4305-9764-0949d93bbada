# 📅 Schedule Calendar API - T<PERSON><PERSON> Hướng Dẫn

## 📋 Tổng Quan

API Calendar cho phép lấy dữ liệu lịch giảng theo khoảng thời gian để hiển thị dưới dạng calendar view. API này được thiết kế đặc biệt cho các thư viện calendar như FullCalendar, DayPilot, hoặc các calendar component khác.

## 🔧 API Endpoint

### **GET** `/api/schedules/calendar`

**Mô tả**: L<PERSON>y lịch giảng theo khoảng thời gian cho calendar view

**Security**: Yêu cầu authentication với roles: `ADMIN`, `TRUONG_KHOA`, hoặc `GIANG_VIEN`

## 📝 Parameters

### **Required Parameters**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `startDate` | String | Ng<PERSON><PERSON> bắt đầ<PERSON> (ISO format) | `2025-05-31` |
| `endDate` | String | Ng<PERSON><PERSON> kết thúc (ISO format) | `2025-06-29` |

### **Optional Parameters**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `semesterId` | Long | ID học kỳ để lọc | `1` |
| `teacherId` | Long | ID giảng viên để lọc | `123` |
| `classId` | Long | ID lớp học để lọc | `456` |
| `departmentId` | Long | ID khoa để lọc | `789` |

## 🔍 Request Examples

### **1. Lấy Tất Cả Lịch Giảng Trong Tháng**
```http
GET /api/schedules/calendar?startDate=2025-05-31&endDate=2025-06-29&semesterId=1
Authorization: Bearer {token}
```

### **2. Lấy Lịch Giảng Của Một Giảng Viên**
```http
GET /api/schedules/calendar?startDate=2025-05-31&endDate=2025-06-29&semesterId=1&teacherId=123
Authorization: Bearer {token}
```

### **3. Lấy Lịch Giảng Của Một Lớp**
```http
GET /api/schedules/calendar?startDate=2025-05-31&endDate=2025-06-29&semesterId=1&classId=456
Authorization: Bearer {token}
```

### **4. Lấy Lịch Giảng Của Một Khoa**
```http
GET /api/schedules/calendar?startDate=2025-05-31&endDate=2025-06-29&semesterId=1&departmentId=789
Authorization: Bearer {token}
```

## 📊 Response Format

### **Success Response (200 OK)**

```json
{
  "success": true,
  "message": "Lấy lịch giảng calendar thành công",
  "data": [
    {
      "id": 1,
      "title": "JAVA001 - P.101",
      "description": "Môn: Lập trình Java\nGV: Nguyễn Văn A\nLớp: CNTT2021A\nPhòng: P.101 (Cơ sở 1)\nBuổi: Sáng\nHình thức: Lý thuyết\nSố tiết: 3",
      "start": "2025-06-02T07:00:00",
      "end": "2025-06-02T09:30:00",
      "allDay": false,
      "backgroundColor": "#3788d8",
      "borderColor": "#3788d8",
      "textColor": "#ffffff",
      "extendedProps": {
        "scheduleId": 1,
        "teacherId": 123,
        "teacherName": "Nguyễn Văn A",
        "subjectCode": "JAVA001",
        "subjectName": "Lập trình Java",
        "classCode": "CNTT2021A",
        "className": "Công nghệ thông tin 2021 A",
        "roomName": "P.101",
        "campusName": "Cơ sở 1",
        "sessionName": "Sáng",
        "formType": "Lý thuyết",
        "practiceGroup": null,
        "credits": 3,
        "coefficient": 1.0,
        "note": null
      }
    },
    {
      "id": 2,
      "title": "JAVA001 (Nhóm 1) - Lab.201",
      "description": "Môn: Lập trình Java\nGV: Nguyễn Văn A\nLớp: CNTT2021A\nPhòng: Lab.201 (Cơ sở 1)\nBuổi: Chiều\nHình thức: Thực hành\nSố tiết: 2",
      "start": "2025-06-02T13:00:00",
      "end": "2025-06-02T15:00:00",
      "allDay": false,
      "backgroundColor": "#28a745",
      "borderColor": "#28a745",
      "textColor": "#ffffff",
      "extendedProps": {
        "scheduleId": 2,
        "teacherId": 123,
        "teacherName": "Nguyễn Văn A",
        "subjectCode": "JAVA001",
        "subjectName": "Lập trình Java",
        "classCode": "CNTT2021A",
        "className": "Công nghệ thông tin 2021 A",
        "roomName": "Lab.201",
        "campusName": "Cơ sở 1",
        "sessionName": "Chiều",
        "formType": "Thực hành",
        "practiceGroup": "Nhóm 1",
        "credits": 2,
        "coefficient": 1.0,
        "note": null
      }
    }
  ]
}
```

### **Error Response (400 Bad Request)**

```json
{
  "success": false,
  "message": "Ngày bắt đầu không được sau ngày kết thúc",
  "data": null
}
```

## 🎨 Event Colors

API tự động gán màu sắc cho các sự kiện dựa trên hình thức học:

| Hình Thức | Màu | Hex Code |
|-----------|-----|----------|
| Lý thuyết (LT) | 🔵 Blue | `#3788d8` |
| Thực hành (TH) | 🟢 Green | `#28a745` |
| Bài tập (BT) | 🟡 Yellow | `#ffc107` |
| Thực nghiệm (TN) | 🔴 Red | `#dc3545` |
| Đồ án (DA) | 🟣 Purple | `#6f42c1` |
| Khác | ⚫ Gray | `#6c757d` |

## 🔒 Security & Permissions

### **Role-Based Access Control**

| Role | Quyền Truy Cập |
|------|----------------|
| **ADMIN** | Xem tất cả lịch giảng |
| **TRUONG_KHOA** | Xem lịch giảng trong khoa |
| **GIANG_VIEN** | Xem lịch giảng cá nhân |

### **Data Filtering**

- API tự động lọc dữ liệu theo quyền của user
- Giảng viên chỉ thấy lịch giảng của mình
- Trưởng khoa chỉ thấy lịch giảng trong khoa
- Admin thấy tất cả

## ⚡ Performance & Limitations

### **Giới Hạn Khoảng Thời Gian**

- **Tối đa**: 3 tháng (90 ngày)
- **Lý do**: Tránh tải quá nhiều dữ liệu, đảm bảo performance

### **Validation Rules**

1. `startDate` phải trước `endDate`
2. Khoảng thời gian không quá 3 tháng
3. Định dạng ngày phải là ISO (YYYY-MM-DD)

## 🔧 Integration Examples

### **FullCalendar.js**

```javascript
$('#calendar').fullCalendar({
    events: function(start, end, timezone, callback) {
        $.ajax({
            url: '/api/schedules/calendar',
            data: {
                startDate: start.format('YYYY-MM-DD'),
                endDate: end.format('YYYY-MM-DD'),
                semesterId: 1
            },
            headers: {
                'Authorization': 'Bearer ' + token
            },
            success: function(response) {
                callback(response.data);
            }
        });
    },
    eventClick: function(event) {
        // Hiển thị chi tiết sự kiện
        showEventDetails(event.extendedProps);
    }
});
```

### **React Calendar**

```jsx
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';

const localizer = momentLocalizer(moment);

function ScheduleCalendar() {
    const [events, setEvents] = useState([]);
    
    const fetchEvents = async (start, end) => {
        const response = await fetch(
            `/api/schedules/calendar?startDate=${start}&endDate=${end}&semesterId=1`,
            {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }
        );
        const data = await response.json();
        setEvents(data.data);
    };
    
    return (
        <Calendar
            localizer={localizer}
            events={events}
            startAccessor="start"
            endAccessor="end"
            titleAccessor="title"
            onNavigate={(date) => {
                const start = moment(date).startOf('month').format('YYYY-MM-DD');
                const end = moment(date).endOf('month').format('YYYY-MM-DD');
                fetchEvents(start, end);
            }}
        />
    );
}
```

### **Vue.js + FullCalendar**

```vue
<template>
  <FullCalendar
    :options="calendarOptions"
    ref="fullCalendar"
  />
</template>

<script>
import FullCalendar from '@fullcalendar/vue'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'

export default {
  components: {
    FullCalendar
  },
  data() {
    return {
      calendarOptions: {
        plugins: [dayGridPlugin, timeGridPlugin],
        initialView: 'dayGridMonth',
        events: this.fetchEvents,
        eventClick: this.handleEventClick
      }
    }
  },
  methods: {
    async fetchEvents(info) {
      const response = await this.$http.get('/api/schedules/calendar', {
        params: {
          startDate: info.startStr.split('T')[0],
          endDate: info.endStr.split('T')[0],
          semesterId: this.selectedSemester
        }
      });
      return response.data.data;
    },
    handleEventClick(info) {
      // Hiển thị popup chi tiết
      this.showEventDetails(info.event.extendedProps);
    }
  }
}
</script>
```

## 🐛 Error Handling

### **Common Errors**

| Error Code | Message | Solution |
|------------|---------|----------|
| 400 | "Ngày bắt đầu không được sau ngày kết thúc" | Kiểm tra lại startDate và endDate |
| 400 | "Khoảng thời gian không được vượt quá 3 tháng" | Giảm khoảng thời gian xuống dưới 90 ngày |
| 401 | "Unauthorized" | Kiểm tra token authentication |
| 403 | "Forbidden" | Kiểm tra quyền truy cập |

### **Error Response Format**

```json
{
  "success": false,
  "message": "Error description",
  "data": null
}
```

## 📈 Advanced Features

### **Week Calculation**

API tự động tính toán tuần học dựa trên:
- Ngày bắt đầu học kỳ
- Chuỗi tuần học trong lịch giảng (VD: "1-15", "1-8,10-15")

### **Recurring Events**

API tự động tạo các sự kiện lặp lại cho:
- Các ngày trong tuần tương ứng
- Các tuần học được chỉ định
- Khoảng thời gian được yêu cầu

### **Rich Metadata**

Mỗi event chứa đầy đủ thông tin trong `extendedProps`:
- Thông tin giảng viên, môn học, lớp
- Thông tin phòng học, cơ sở
- Thông tin buổi học, hình thức
- Ghi chú và thông tin bổ sung

---

## 📞 Support

Để được hỗ trợ hoặc báo lỗi, vui lòng liên hệ team phát triển.

**Status**: ✅ **HOÀN THÀNH**  
**Version**: 1.0.0  
**Last Updated**: 2024
