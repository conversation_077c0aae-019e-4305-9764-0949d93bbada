import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTit<PERSON> } from '@/components/ui/card'
import { LucideIcon } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface QuickActionCardProps {
  title: string
  description: string
  icon: LucideIcon
  href: string
  color?: string
}

export default function QuickActionCard({ 
  title, 
  description, 
  icon: Icon, 
  href,
  color = "bg-blue-500"
}: QuickActionCardProps) {
  const router = useRouter()

  return (
    <Card 
      className="hover:shadow-lg transition-shadow cursor-pointer"
      onClick={() => router.push(href)}
    >
      <CardHeader>
        <div className={`w-12 h-12 ${color} rounded-lg flex items-center justify-center mb-4`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  )
}
