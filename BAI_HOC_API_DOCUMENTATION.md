# 📚 API Quản Lý Bài Học - Tài Liệu Hướng Dẫn

## 📋 Tổng Quan

Hệ thống quản lý bài học cho phép quản lý chi tiết các bài học trong từng môn học, bao gồm n<PERSON> dung, mụ<PERSON> tiêu, ph<PERSON>ơng pháp giảng dạy và đánh giá.

## 🏗️ Kiến Trúc Hệ Thống

### 1. **Entity - BaiHoc**
```java
@Entity
@Table(name = "BAI_HOC")
public class BaiHoc extends BaseEntity {
    private Long idBaiHoc;           // ID bài học
    private Long idMonHoc;           // ID môn học (FK)
    private String maBaiHoc;         // Mã bài học (unique)
    private String tenBaiHoc;        // Tên bài học
    private Integer sttBaiHoc;       // S<PERSON> thứ tự trong môn
    private Integer soTietLt;        // Số tiết lý thuyết
    private Integer soTietTh;        // Số tiết thực hành
    private Integer soTietTu;        // Số tiết tự học
    private String mucTieu;          // Mục tiêu bài học
    private String noiDung;          // Nội dung chi tiết
    private String phuongPhapDay;    // Phương pháp giảng dạy
    private String taiLieuThamKhao;  // Tài liệu tham khảo
    private String baiTap;           // Bài tập về nhà
    private String danhGia;          // Phương pháp đánh giá
    private String ghiChu;           // Ghi chú
    private Boolean trangThai;       // Trạng thái hoạt động
}
```

### 2. **Quan Hệ Dữ Liệu**
- **MonHoc** (1) ↔ (N) **BaiHoc**: Một môn học có nhiều bài học
- **BaiHoc** có ràng buộc unique trên `(idMonHoc, sttBaiHoc)` - không trùng số thứ tự trong cùng môn

### 3. **Cấu Trúc Thư Mục**
```
src/main/java/com/university/schedulemanagement/
├── entity/
│   └── BaiHoc.java                    ✅ Entity chính
├── dto/request/
│   └── BaiHocRequest.java             ✅ DTO cho request
├── repository/
│   └── BaiHocRepository.java          ✅ Repository với các query tùy chỉnh
├── service/
│   └── BaiHocService.java             ✅ Service interface
├── serviceImpl/
│   └── BaiHocServiceImpl.java         ✅ Service implementation
└── controller/
    └── BaiHocController.java          ✅ REST Controller
```

## 🔧 API Endpoints

### **Base URL**: `/api/bai-hoc`

### 1. **CRUD Operations**

#### 📖 Lấy Danh Sách Bài Học
```http
GET /api/bai-hoc
```
**Parameters:**
- `page` (int): Số trang (default: 0)
- `size` (int): Kích thước trang (default: 10)
- `sortBy` (string): Trường sắp xếp (default: "sttBaiHoc")
- `sortDir` (string): Hướng sắp xếp "asc"/"desc" (default: "asc")

#### 📖 Lấy Bài Học Theo Môn
```http
GET /api/bai-hoc/mon-hoc/{idMonHoc}
```

#### 📖 Lấy Tất Cả Bài Học Theo Môn (Không Phân Trang)
```http
GET /api/bai-hoc/mon-hoc/{idMonHoc}/all
```

#### 🔍 Tìm Kiếm Bài Học
```http
GET /api/bai-hoc/search?keyword={keyword}
```

#### 📖 Lấy Chi Tiết Bài Học
```http
GET /api/bai-hoc/{id}
```

#### ➕ Tạo Bài Học Mới
```http
POST /api/bai-hoc
Content-Type: application/json

{
    "idMonHoc": 1,
    "maBaiHoc": "BH001",
    "tenBaiHoc": "Giới thiệu về Java",
    "sttBaiHoc": 1,
    "soTietLt": 2,
    "soTietTh": 1,
    "soTietTu": 1,
    "mucTieu": "Hiểu được tổng quan về ngôn ngữ Java",
    "noiDung": "Lịch sử, đặc điểm, ưu nhược điểm của Java",
    "phuongPhapDay": "Thuyết trình, thảo luận",
    "taiLieuThamKhao": "Java: The Complete Reference",
    "baiTap": "Đọc chương 1, làm bài tập 1-5",
    "danhGia": "Kiểm tra trắc nghiệm",
    "ghiChu": "Bài học mở đầu",
    "trangThai": true
}
```

#### ✏️ Cập Nhật Bài Học
```http
PUT /api/bai-hoc/{id}
Content-Type: application/json
```

#### 🗑️ Xóa Bài Học
```http
DELETE /api/bai-hoc/{id}
```

#### 🔄 Thay Đổi Trạng Thái
```http
PATCH /api/bai-hoc/{id}/toggle-status
```

### 2. **Utility Endpoints**

#### 📊 Thống Kê Bài Học Theo Môn
```http
GET /api/bai-hoc/statistics/mon-hoc/{idMonHoc}
```

#### 🔢 Tạo Số Thứ Tự Tiếp Theo
```http
GET /api/bai-hoc/generate-stt/{idMonHoc}
```

## 🔒 Phân Quyền

### **Roles và Permissions:**
- **ADMIN**: Toàn quyền (CRUD, thống kê)
- **TRUONG_KHOA**: Toàn quyền trong khoa
- **GIANG_VIEN**: Xem, tạo, sửa bài học (không xóa)

### **Security Matrix:**
| Endpoint | ADMIN | TRUONG_KHOA | GIANG_VIEN |
|----------|-------|-------------|------------|
| GET (All) | ✅ | ✅ | ✅ |
| POST | ✅ | ✅ | ✅ |
| PUT | ✅ | ✅ | ✅ |
| DELETE | ✅ | ✅ | ❌ |
| Toggle Status | ✅ | ✅ | ❌ |

## 💾 Database Schema

### **Bảng BAI_HOC**
```sql
CREATE TABLE BAI_HOC (
    ID_BAI_HOC BIGINT AUTO_INCREMENT PRIMARY KEY,
    ID_MON_HOC BIGINT NOT NULL,
    MA_BAI_HOC VARCHAR(20) NOT NULL UNIQUE,
    TEN_BAI_HOC VARCHAR(250) NOT NULL,
    STT_BAI_HOC INT,
    SO_TIET_LT INT DEFAULT 0,
    SO_TIET_TH INT DEFAULT 0,
    SO_TIET_TU INT DEFAULT 0,
    MUC_TIEU TEXT,
    NOI_DUNG TEXT,
    PHUONG_PHAP_DAY TEXT,
    TAI_LIEU_THAM_KHAO TEXT,
    BAI_TAP TEXT,
    DANH_GIA TEXT,
    GHI_CHU TEXT,
    TRANG_THAI BOOLEAN DEFAULT TRUE,
    NGAY_TAO TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    NGAY_CAP_NHAT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_bai_hoc_mon_stt (ID_MON_HOC, STT_BAI_HOC),
    FOREIGN KEY (ID_MON_HOC) REFERENCES MON_HOC(ID_MON_HOC)
);
```

### **Indexes và Constraints**
- **Primary Key**: `ID_BAI_HOC`
- **Unique Keys**: 
  - `MA_BAI_HOC` (mã bài học duy nhất)
  - `(ID_MON_HOC, STT_BAI_HOC)` (số thứ tự duy nhất trong môn)
- **Foreign Key**: `ID_MON_HOC → MON_HOC.ID_MON_HOC`
- **Check Constraints**: Số tiết >= 0, ít nhất 1 loại tiết > 0

## 📊 Business Logic

### **Validation Rules**
1. **Mã bài học**: Unique toàn hệ thống
2. **Số thứ tự**: Unique trong từng môn học
3. **Số tiết**: Phải >= 0, ít nhất 1 loại tiết > 0
4. **Môn học**: Phải tồn tại trong hệ thống

### **Auto-Generation**
- **Số thứ tự**: Tự động tạo số tiếp theo nếu không cung cấp
- **Timestamps**: Tự động cập nhật ngày tạo/sửa

### **Cascade Operations**
- **Xóa môn học**: Tự động xóa tất cả bài học liên quan
- **Cập nhật môn học**: Tự động cập nhật references

## 🔍 Query Examples

### **Repository Methods**
```java
// Tìm theo môn học và sắp xếp theo STT
List<BaiHoc> findByIdMonHocOrderBySttBaiHoc(Long idMonHoc);

// Tìm kiếm theo từ khóa
Page<BaiHoc> findByKeyword(String keyword, Pageable pageable);

// Thống kê số tiết
Integer sumTotalHoursByIdMonHoc(Long idMonHoc);

// Kiểm tra trùng lặp
boolean existsByIdMonHocAndSttBaiHoc(Long idMonHoc, Integer sttBaiHoc);
```

### **Custom Queries**
```java
@Query("SELECT bh FROM BaiHoc bh WHERE bh.idMonHoc = :idMonHoc AND " +
       "(LOWER(bh.tenBaiHoc) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
       "LOWER(bh.noiDung) LIKE LOWER(CONCAT('%', :keyword, '%')))")
Page<BaiHoc> findByIdMonHocAndKeyword(@Param("idMonHoc") Long idMonHoc, 
                                      @Param("keyword") String keyword, 
                                      Pageable pageable);
```

## 🚀 Deployment

### **1. Chạy SQL Script**
```bash
mysql -u username -p database_name < database/create_bai_hoc_table.sql
```

### **2. Build và Deploy**
```bash
mvn clean compile
mvn spring-boot:run
```

### **3. Test API**
```bash
# Lấy danh sách bài học
curl -X GET "http://localhost:8080/api/bai-hoc" \
     -H "Authorization: Bearer {token}"

# Tạo bài học mới
curl -X POST "http://localhost:8080/api/bai-hoc" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer {token}" \
     -d '{
       "idMonHoc": 1,
       "maBaiHoc": "BH001",
       "tenBaiHoc": "Bài học mẫu",
       "sttBaiHoc": 1,
       "soTietLt": 2,
       "soTietTh": 1
     }'
```

## 📈 Future Enhancements

### **Planned Features**
- ✅ **Copy/Clone Bài Học**: Sao chép bài học giữa các môn
- ✅ **Reorder Bài Học**: Sắp xếp lại thứ tự bài học
- ✅ **Excel Import/Export**: Nhập/xuất dữ liệu Excel
- ✅ **Advanced Statistics**: Thống kê chi tiết theo khoa/học kỳ
- ✅ **Template Management**: Quản lý mẫu bài học

### **Technical Improvements**
- **Caching**: Redis cache cho queries thường dùng
- **Search**: Elasticsearch cho tìm kiếm nâng cao
- **Audit**: Lưu lịch sử thay đổi bài học
- **Versioning**: Quản lý phiên bản bài học

---

## 📞 Support

Để được hỗ trợ hoặc báo lỗi, vui lòng liên hệ team phát triển hoặc tạo issue trong repository.

**Status**: ✅ **HOÀN THÀNH**  
**Version**: 1.0.0  
**Last Updated**: 2024
