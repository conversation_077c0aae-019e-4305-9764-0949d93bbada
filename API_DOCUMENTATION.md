# 📚 API Documentation - Schedule Management System

## 🌐 Base URL
```
http://localhost:8080/api
```

## 🔐 Authentication
All protected endpoints require Bearer token in Authorization header:
```
Authorization: Bearer {token}
```

## 📋 API Endpoints Overview

### 🔑 Authentication APIs (`/auth`)

#### 1. Login
```http
POST /auth/login
Content-Type: application/json

{
  "maCanBo": "string",
  "matKhau": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Đăng nhập thành công",
  "data": {
    "token": "string",
    "tokenType": "Bearer",
    "expiresIn": 86400,
    "userInfo": {
      "id": 1,
      "maCanBo": "string",
      "ten": "string",
      "email": "string",
      "sdt": "string",
      "tenVaiTro": "string",
      "tenKhoa": "string",
      "nu": false
    }
  }
}
```

#### 2. Logout
```http
POST /auth/logout
Authorization: Bearer {token}
```

#### 3. Change Password
```http
POST /auth/change-password
Authorization: Bearer {token}
Content-Type: application/json

{
  "matKhauCu": "string",
  "matKhauMoi": "string",
  "xacNhanMatKhau": "string"
}
```

#### 4. Get Current User
```http
GET /auth/me
Authorization: Bearer {token}
```

### 📅 Schedule Management APIs (`/schedules`)

#### 1. Create Schedule (7-Step Process)
```http
POST /schedules
Authorization: Bearer {token}
Content-Type: application/json

{
  "idLop": 1,
  "idHinhThuc": 1,
  "nhomTh": "string",
  "idMonHoc": 1,
  "soTiet": 4,
  "heSo": 1.0,
  "idCanBo": 1,
  "thuHoc": 2,
  "idBuoi": 1,
  "idPhong": 1,
  "tuanHoc": "1-15",
  "ghiChu": "string",
  "idHocKy": 1
}
```

#### 2. Get Schedules by Teacher
```http
GET /schedules/teacher/{teacherId}?semesterId={semesterId}
Authorization: Bearer {token}
```

#### 3. Get Schedules by Class
```http
GET /schedules/class/{classId}?semesterId={semesterId}
Authorization: Bearer {token}
```

#### 4. Get Schedules by Semester (Paginated)
```http
GET /schedules/semester/{semesterId}?page=0&size=10&sortBy=createdAt&sortDir=desc
Authorization: Bearer {token}
```

#### 5. Get Personal Schedule
```http
GET /schedules/personal?semesterId={semesterId}
Authorization: Bearer {token}
```

#### 6. Check Schedule Conflict
```http
POST /schedules/check-conflict
Authorization: Bearer {token}
Content-Type: application/json

{
  "idCanBo": 1,
  "idPhong": 1,
  "idLop": 1,
  "thuHoc": 2,
  "idBuoi": 1,
  "idHocKy": 1
}
```

#### 7. Get Available Rooms
```http
GET /schedules/available-rooms?coSoId=1&loaiPhong=LT&thuHoc=2&buoiId=1&hocKyId=1
Authorization: Bearer {token}
```

#### 8. Delete Schedule
```http
DELETE /schedules/{id}
Authorization: Bearer {token}
```

#### 9. Export Personal Schedule
```http
GET /schedules/export/personal?semesterId={semesterId}
Authorization: Bearer {token}
```

#### 10. Export Teacher Schedule
```http
GET /schedules/export/teacher/{teacherId}?semesterId={semesterId}
Authorization: Bearer {token}
```

### ⏰ Teaching Hours APIs (`/teaching-hours`)

#### 1. Calculate Teaching Hours
```http
POST /teaching-hours/calculate?teacherId={teacherId}&semesterId={semesterId}
Authorization: Bearer {token}
```

#### 2. Get Teaching Hours by Teacher
```http
GET /teaching-hours/teacher/{teacherId}?semesterId={semesterId}
Authorization: Bearer {token}
```

#### 3. Get Teaching Hours by Department
```http
GET /teaching-hours/department/{departmentId}?semesterId={semesterId}
Authorization: Bearer {token}
```

#### 4. Get Personal Teaching Hours
```http
GET /teaching-hours/personal?semesterId={semesterId}
Authorization: Bearer {token}
```

#### 5. Export Teaching Hours Report
```http
GET /teaching-hours/export?departmentId={departmentId}&semesterId={semesterId}
Authorization: Bearer {token}
```

### 🎓 Academic Year Management (`/academic-years`)

#### 1. Get All Academic Years
```http
GET /academic-years?page=0&size=10
Authorization: Bearer {token}
```

#### 2. Get Academic Year by ID
```http
GET /academic-years/{id}
Authorization: Bearer {token}
```

#### 3. Create Academic Year
```http
POST /academic-years
Authorization: Bearer {token}
Content-Type: application/json

{
  "tenNienKhoa": "string",
  "nam": 2024,
  "idThongTu": 1,
  "moTa": "string",
  "trangThai": true
}
```

#### 4. Update Academic Year
```http
PUT /academic-years/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "tenNienKhoa": "string",
  "nam": 2024,
  "idThongTu": 1,
  "moTa": "string",
  "trangThai": true
}
```

#### 5. Delete Academic Year
```http
DELETE /academic-years/{id}
Authorization: Bearer {token}
```

### 📚 Semester Management (`/semesters`)

#### 1. Get All Semesters
```http
GET /semesters?academicYearId={academicYearId}&page=0&size=10
Authorization: Bearer {token}
```

#### 2. Get Semester by ID
```http
GET /semesters/{id}
Authorization: Bearer {token}
```

#### 3. Create Semester
```http
POST /semesters
Authorization: Bearer {token}
Content-Type: application/json

{
  "idNienKhoa": 1,
  "tenHocKy": "string",
  "soTuan": 15,
  "ngayBatDau": "2024-01-01",
  "ngayKetThuc": "2024-05-31",
  "hienTai": false,
  "moTa": "string",
  "trangThai": true
}
```

#### 4. Update Semester
```http
PUT /semesters/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "idNienKhoa": 1,
  "tenHocKy": "string",
  "soTuan": 15,
  "ngayBatDau": "2024-01-01",
  "ngayKetThuc": "2024-05-31",
  "hienTai": false,
  "moTa": "string",
  "trangThai": true
}
```

#### 5. Delete Semester
```http
DELETE /semesters/{id}
Authorization: Bearer {token}
```

#### 6. Set Current Semester
```http
PUT /semesters/{id}/set-current
Authorization: Bearer {token}
```

#### 7. Get Semester Statistics
```http
GET /semesters/{id}/statistics
Authorization: Bearer {token}
```

### 🏢 Master Data APIs (`/master-data`)

#### 1. Get All Departments
```http
GET /master-data/departments?page=0&size=10
Authorization: Bearer {token}
```

#### 2. Get All Subjects
```http
GET /master-data/subjects?departmentId={departmentId}&page=0&size=10
Authorization: Bearer {token}
```

#### 3. Get All Classes
```http
GET /master-data/classes?departmentId={departmentId}&page=0&size=10
Authorization: Bearer {token}
```

#### 4. Get All Teachers
```http
GET /master-data/teachers?departmentId={departmentId}&page=0&size=10
Authorization: Bearer {token}
```

#### 5. Get All Rooms
```http
GET /master-data/rooms?campusId={campusId}&page=0&size=10
Authorization: Bearer {token}
```

#### 6. Get All Campuses
```http
GET /master-data/campuses
Authorization: Bearer {token}
```

#### 7. Import Data
```http
POST /master-data/import?dataType={dataType}
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: [Excel file]
```

### 🏥 Health Check APIs (`/health`)

#### 1. Health Check
```http
GET /health
```

#### 2. System Info
```http
GET /health/info
```

### 👨‍💼 Admin APIs (`/admin`)

#### 1. Get Dashboard Data
```http
GET /admin/dashboard
Authorization: Bearer {token}
```

#### 2. Get System Statistics
```http
GET /admin/statistics
Authorization: Bearer {token}
```

#### 3. Create Backup
```http
POST /admin/backup
Authorization: Bearer {token}
```

### 📥 Import/Sync APIs (`/import`)

#### 1. Sync Data from Source
```http
POST /import/sync/{type}
Authorization: Bearer {token}
```

#### 2. Upload File Import
```http
POST /import/upload?type={type}
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: [File to import]
```

#### 3. Get Import Status
```http
GET /import/status
Authorization: Bearer {token}
```

#### 4. Test Source Connection
```http
GET /import/test-connection
Authorization: Bearer {token}
```

## 📊 Common Response Structure

### Success Response
```json
{
  "success": true,
  "message": "Success message",
  "data": { ... },
  "metadata": null
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "data": null,
  "metadata": { ... }
}
```

### Paginated Response
```json
{
  "success": true,
  "message": "Success message",
  "data": {
    "content": [...],
    "page": 0,
    "size": 10,
    "totalElements": 100,
    "totalPages": 10,
    "first": true,
    "last": false,
    "hasNext": true,
    "hasPrevious": false
  }
}
```

## 🔒 Role-Based Access Control

### Roles:
- **ADMIN**: Full system access
- **TRUONG_KHOA**: Department head access
- **GIANG_VIEN**: Teacher access

### Permission Matrix:
| Endpoint | ADMIN | TRUONG_KHOA | GIANG_VIEN |
|----------|-------|-------------|------------|
| Create Schedule | ✅ | ✅ | ❌ |
| View All Schedules | ✅ | ✅ | ❌ |
| View Personal Schedule | ✅ | ✅ | ✅ |
| Manage Academic Years | ✅ | ❌ | ❌ |
| Manage Semesters | ✅ | ❌ | ❌ |
| View Teaching Hours | ✅ | ✅ | ✅ (own only) |
| Export Reports | ✅ | ✅ | ✅ (own only) |
| Import Data | ✅ | ❌ | ❌ |
| Admin Dashboard | ✅ | ❌ | ❌ |

## 🚀 Getting Started

1. **Authentication**: Start with `/auth/login` to get access token
2. **Get Current User**: Use `/auth/me` to get user info and permissions
3. **Load Master Data**: Get departments, subjects, classes, etc.
4. **Create/View Schedules**: Use schedule endpoints based on user role
5. **Generate Reports**: Export schedules and teaching hours

## 📋 Data Transfer Objects (DTOs)

### Request DTOs

#### LoginRequest
```typescript
interface LoginRequest {
  maCanBo: string;      // Required
  matKhau: string;      // Required
}
```

#### ScheduleRequest
```typescript
interface ScheduleRequest {
  idLop: number;        // Required - Class ID
  idHinhThuc: number;   // Required - Learning format ID
  nhomTh?: string;      // Optional - Practice group
  idMonHoc: number;     // Required - Subject ID
  soTiet: number;       // Required - Number of periods (min: 1)
  heSo: number;         // Coefficient (0.0-2.2, default: 1.0)
  idCanBo: number;      // Required - Teacher ID
  thuHoc: number;       // Required - Day of week (2-8)
  idBuoi: number;       // Required - Session ID
  idPhong: number;      // Required - Room ID
  tuanHoc?: string;     // Optional - Week range (e.g., "1-15")
  ghiChu?: string;      // Optional - Notes
  idHocKy: number;      // Required - Semester ID
}
```

#### AcademicYearRequest
```typescript
interface AcademicYearRequest {
  tenNienKhoa: string;  // Required - Academic year name
  nam: number;          // Required - Year (2020-2050)
  idThongTu?: number;   // Optional - Circular ID
  moTa?: string;        // Optional - Description
  trangThai: boolean;   // Status (default: true)
}
```

#### SemesterRequest
```typescript
interface SemesterRequest {
  idNienKhoa: number;   // Required - Academic year ID
  tenHocKy: string;     // Required - Semester name
  soTuan: number;       // Number of weeks (1-52)
  ngayBatDau: string;   // Required - Start date (YYYY-MM-DD)
  ngayKetThuc: string;  // Required - End date (YYYY-MM-DD)
  hienTai: boolean;     // Is current semester (default: false)
  moTa?: string;        // Optional - Description
  trangThai: boolean;   // Status (default: true)
}
```

#### ChangePasswordRequest
```typescript
interface ChangePasswordRequest {
  matKhauCu: string;    // Required - Current password
  matKhauMoi: string;   // Required - New password (min: 6 chars)
  xacNhanMatKhau: string; // Required - Confirm password
}
```

#### TeachingHourRequest
```typescript
interface TeachingHourRequest {
  teacherId: number;    // Required - Teacher ID
  semesterId: number;   // Required - Semester ID
  year?: number;        // Optional - Year filter
  month?: number;       // Optional - Month filter
}
```

#### FilterRequest
```typescript
interface FilterRequest {
  keyword?: string;
  departmentId?: number;
  semesterId?: number;
  educationLevelId?: number;
  majorId?: number;
  classId?: number;
  teacherId?: number;
  fromDate?: string;    // YYYY-MM-DD
  toDate?: string;      // YYYY-MM-DD
  status?: boolean;
  sortBy: string;       // Default: "id"
  sortDirection: string; // "ASC" | "DESC", Default: "ASC"
  page: number;         // Default: 0
  size: number;         // Default: 10
}
```

### Response DTOs

#### LoginResponse
```typescript
interface LoginResponse {
  token: string;
  tokenType: string;    // "Bearer"
  expiresIn: number;    // Seconds
  userInfo: {
    id: number;
    maCanBo: string;
    ten: string;
    email: string;
    sdt: string;
    tenVaiTro: string;
    tenKhoa: string;
    nu: boolean;
  };
}
```

#### ScheduleResponse
```typescript
interface ScheduleResponse {
  id: number;
  maMonHoc: string;
  tenMonHoc: string;
  tenGiangVien: string;
  maLop: string;
  tenLop: string;
  hinhThucHoc: string;
  thuHoc: number;
  thuHocText: string;
  tenBuoi: string;
  gioBatDau: string;    // HH:mm:ss
  gioKetThuc: string;   // HH:mm:ss
  tenPhong: string;
  tenCoSo: string;
  soTiet: number;
  heSo: number;
  soGioQuyDoi: number;
  nhomTh?: string;
  tuanHoc?: string;
  ghiChu?: string;
  trangThai: boolean;
  createdAt: string;    // ISO datetime
  updatedAt: string;    // ISO datetime
}
```

#### TeachingHourResponse
```typescript
interface TeachingHourResponse {
  teacherId: number;
  maCanBo: string;
  tenCanBo: string;
  tenKhoa: string;
  semesterId: number;
  tenHocKy: string;
  tongGioLt: number;    // Total theory hours
  tongGioTh: number;    // Total practice hours
  tongGioTong: number;  // Total hours
  tongGioQuyDoi: number; // Total converted hours
  tyLePhanBo: string;   // Distribution ratio
  ngayTinh: string;     // ISO datetime
  lichGiangList: ScheduleResponse[];
}
```

#### AcademicYearResponse
```typescript
interface AcademicYearResponse {
  idNienKhoa: number;
  tenNienKhoa: string;
  nam: number;
  idThongTu?: number;
  moTa?: string;
  trangThai: boolean;
  ngayTao: string;      // ISO datetime
  ngayCapNhat: string;  // ISO datetime
  soHocKy: number;      // Number of semesters
  soLichGiang: number;  // Number of schedules
  isActive: boolean;
  hocKyList?: SemesterResponse[];
}
```

#### SemesterResponse
```typescript
interface SemesterResponse {
  idHocKy: number;
  idNienKhoa: number;
  tenHocKy: string;
  soTuan: number;
  ngayBatDau: string;   // YYYY-MM-DD
  ngayKetThuc: string;  // YYYY-MM-DD
  hienTai: boolean;
  moTa?: string;
  trangThai: boolean;
  ngayTao: string;      // ISO datetime
  ngayCapNhat: string;  // ISO datetime
  tenNienKhoa: string;
  namNienKhoa: number;
  soLichGiang: number;
  soGiangVien: number;
  soLopHoc: number;
  soMonHoc: number;
  trangThaiHocKy: string; // "CHUA_BAT_DAU" | "DANG_DIEN_RA" | "DA_KET_THUC"
  soNgayConLai: number;
}
```

#### DashboardResponse
```typescript
interface DashboardResponse {
  totalTeachers: number;
  totalSubjects: number;
  totalClasses: number;
  totalSchedules: number;
  totalRooms: number;
  totalDepartments: number;
  totalAcademicYears: number;
  totalSemesters: number;
  totalTeachingHours: number;
  currentSemesterId: number;
  currentSemester: string;
  currentSemesterName: string;
  weeklySchedules: number;
  departmentStats: {
    departmentId: number;
    departmentName: string;
    totalTeachers: number;
    totalSubjects: number;
    totalTeachingHours: number;
  }[];
  monthlyStats: {
    monthName: string;
    totalSchedules: number;
    totalTeachingHours: number;
  }[];
}
```

#### ConflictCheckResponse
```typescript
interface ConflictCheckResponse {
  hasConflict: boolean;
  message: string;
  conflictType: string;
  conflicts: {
    type: string;         // "TEACHER" | "ROOM" | "CLASS"
    description: string;
    scheduleId: number;
    scheduleName: string;
  }[];
}
```

#### AvailableRoomResponse
```typescript
interface AvailableRoomResponse {
  roomId: number;
  maPhong: string;
  tenPhong: string;
  loaiPhong: string;
  sucChua: number;
  tenCoSo: string;
  maCoSo: string;
  isAvailable: boolean;
  description: string;
}
```

#### PageResponse<T>
```typescript
interface PageResponse<T> {
  content: T[];
  page: number;
  size: number;
  totalElements: number;
  totalPages: number;
  first: boolean;
  last: boolean;
  hasNext: boolean;
  hasPrevious: boolean;
}
```

#### ApiResponse<T>
```typescript
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  metadata?: any;
}
```

## 🔧 Error Handling

### HTTP Status Codes
- **200**: Success
- **400**: Bad Request (validation errors)
- **401**: Unauthorized (invalid/missing token)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found
- **500**: Internal Server Error

### Common Error Responses
```json
{
  "success": false,
  "message": "Validation failed",
  "data": null,
  "metadata": {
    "errors": [
      {
        "field": "maCanBo",
        "message": "Mã cán bộ không được để trống"
      }
    ]
  }
}
```

## 📝 Notes

- All dates are in ISO format (YYYY-MM-DD)
- All times are in HH:mm:ss format
- All datetimes are in ISO format (YYYY-MM-DDTHH:mm:ss)
- File uploads support Excel (.xlsx) format
- Pagination starts from page 0
- Default page size is 10, maximum is 100
- Bearer token expires in 24 hours (86400 seconds)
- Vietnamese characters are supported in all text fields
- Time zone is assumed to be Vietnam (UTC+7)
