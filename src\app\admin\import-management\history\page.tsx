'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  ArrowLeft,
  History,
  Search,
  Filter,
  Download,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Upload,
  Database,
  Calendar,
  FileText
} from 'lucide-react'
import { importService, ImportHistory } from '@/lib/services/importService'
import { formatDate } from '@/lib/utils'

const DATA_TYPE_NAMES = {
  subjects: '<PERSON><PERSON><PERSON> học',
  classes: '<PERSON>ớ<PERSON> học', 
  teachers: '<PERSON><PERSON><PERSON><PERSON> viên',
  rooms: '<PERSON><PERSON><PERSON> học',
  academic_years: '<PERSON><PERSON><PERSON> học/Học kỳ'
}

export default function ImportHistoryPage() {
  const router = useRouter()
  const [history, setHistory] = useState<ImportHistory[]>([])
  const [filteredHistory, setFilteredHistory] = useState<ImportHistory[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterAction, setFilterAction] = useState<string>('all')

  useEffect(() => {
    loadHistory()
  }, [])

  useEffect(() => {
    filterHistory()
  }, [history, searchTerm, filterType, filterStatus, filterAction])

  const loadHistory = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await importService.getImportHistory()
      setHistory(data)
    } catch (err) {
      setError('Không thể tải lịch sử import')
      console.error('Error loading import history:', err)
    } finally {
      setLoading(false)
    }
  }

  const filterHistory = () => {
    let filtered = [...history]

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(item => 
        item.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.fileName && item.fileName.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(item => item.type === filterType)
    }

    // Filter by status
    if (filterStatus !== 'all') {
      filtered = filtered.filter(item => item.status === filterStatus)
    }

    // Filter by action
    if (filterAction !== 'all') {
      filtered = filtered.filter(item => item.action === filterAction)
    }

    setFilteredHistory(filtered)
  }

  const getStatusBadge = (status: ImportHistory['status']) => {
    switch (status) {
      case 'success':
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Thành công
          </Badge>
        )
      case 'error':
        return (
          <Badge className="bg-red-100 text-red-800">
            <AlertCircle className="w-3 h-3 mr-1" />
            Lỗi
          </Badge>
        )
      default:
        return <Badge variant="secondary">Không xác định</Badge>
    }
  }

  const getActionBadge = (action: ImportHistory['action']) => {
    switch (action) {
      case 'sync':
        return (
          <Badge variant="outline" className="text-blue-600 border-blue-200">
            <Database className="w-3 h-3 mr-1" />
            Đồng bộ
          </Badge>
        )
      case 'upload':
        return (
          <Badge variant="outline" className="text-purple-600 border-purple-200">
            <Upload className="w-3 h-3 mr-1" />
            Upload
          </Badge>
        )
      default:
        return <Badge variant="secondary">Không xác định</Badge>
    }
  }

  const exportHistory = () => {
    const csvContent = [
      ['Thời gian', 'Loại dữ liệu', 'Hành động', 'Trạng thái', 'Số lượng', 'File', 'Thông báo'],
      ...filteredHistory.map(item => [
        formatDate(item.timestamp),
        DATA_TYPE_NAMES[item.type as keyof typeof DATA_TYPE_NAMES] || item.type,
        item.action === 'sync' ? 'Đồng bộ' : 'Upload',
        item.status === 'success' ? 'Thành công' : 'Lỗi',
        item.count.toString(),
        item.fileName || '',
        item.message
      ])
    ].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `import_history_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (loading && history.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center">
          <RefreshCw className="w-6 h-6 animate-spin mr-2" />
          Đang tải lịch sử...
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => router.push('/admin/import-management')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại
              </Button>
              <History className="h-6 w-6 text-blue-600 mr-3" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Lịch sử Import & Đồng bộ
                </h1>
                <p className="text-sm text-gray-500">
                  Xem chi tiết lịch sử các lần import và đồng bộ dữ liệu
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button onClick={loadHistory} variant="outline" size="sm">
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Làm mới
              </Button>
              <Button onClick={exportHistory} variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Xuất CSV
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              Lịch sử Import & Đồng bộ
            </CardTitle>
            <CardDescription>
              Tổng cộng {filteredHistory.length} bản ghi
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex flex-wrap gap-4 mb-6">
              <div className="flex-1 min-w-[200px]">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Tìm kiếm..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Loại dữ liệu" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả loại</SelectItem>
                  {Object.entries(DATA_TYPE_NAMES).map(([key, name]) => (
                    <SelectItem key={key} value={key}>{name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="success">Thành công</SelectItem>
                  <SelectItem value="error">Lỗi</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterAction} onValueChange={setFilterAction}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Hành động" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="sync">Đồng bộ</SelectItem>
                  <SelectItem value="upload">Upload</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* History Table */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Thời gian</TableHead>
                    <TableHead>Loại dữ liệu</TableHead>
                    <TableHead>Hành động</TableHead>
                    <TableHead>Trạng thái</TableHead>
                    <TableHead>Số lượng</TableHead>
                    <TableHead>File</TableHead>
                    <TableHead>Thông báo</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredHistory.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                        {loading ? 'Đang tải...' : 'Không có dữ liệu'}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredHistory.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                            {formatDate(item.timestamp)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">
                            {DATA_TYPE_NAMES[item.type as keyof typeof DATA_TYPE_NAMES] || item.type}
                          </span>
                        </TableCell>
                        <TableCell>
                          {getActionBadge(item.action)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(item.status)}
                        </TableCell>
                        <TableCell>
                          <span className="font-mono">{item.count.toLocaleString()}</span>
                        </TableCell>
                        <TableCell>
                          {item.fileName ? (
                            <span className="text-sm text-gray-600">{item.fileName}</span>
                          ) : (
                            <span className="text-sm text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="max-w-xs">
                            <p className="text-sm truncate" title={item.message}>
                              {item.message}
                            </p>
                            {item.errors && item.errors.length > 0 && (
                              <p className="text-xs text-red-600 mt-1">
                                {item.errors.length} lỗi
                              </p>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
