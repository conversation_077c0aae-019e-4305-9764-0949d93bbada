// Test file to demonstrate schedule database integration
import { scheduleService, Schedule } from '@/lib/services/scheduleService'

// Example usage of the schedule service
export async function testScheduleDatabaseIntegration() {
  try {
    console.log('🧪 Testing Schedule Database Integration...')
    
    // Test 1: Get all schedules
    console.log('📅 Fetching all schedules...')
    const allSchedules = await scheduleService.getAllSchedules({
      page: 0,
      size: 10,
      sort: 'scheduleDate,desc'
    })
    console.log('✅ All schedules loaded:', allSchedules)
    
    // Test 2: Get schedules by teacher
    console.log('👨‍🏫 Fetching schedules for teacher ID: 1')
    const teacherSchedules = await scheduleService.getSchedulesByTeacher(1)
    console.log('✅ Teacher schedules loaded:', teacherSchedules)
    
    // Test 3: Get schedules for calendar
    const today = new Date()
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    
    console.log('📆 Fetching schedules for calendar view...')
    const calendarSchedules = await scheduleService.getSchedulesForCalendar({
      startDate: startOfMonth.toISOString().split('T')[0],
      endDate: endOfMonth.toISOString().split('T')[0],
      semesterId: 1
    })
    console.log('✅ Calendar schedules loaded:', calendarSchedules)
    
    // Test 4: Get schedules by semester
    console.log('🎓 Fetching schedules for semester ID: 1')
    const semesterSchedules = await scheduleService.getSchedulesBySemester(1, {
      page: 0,
      size: 20
    })
    console.log('✅ Semester schedules loaded:', semesterSchedules)
    
    return {
      success: true,
      allSchedulesCount: Array.isArray(allSchedules) ? allSchedules.length : allSchedules.content?.length || 0,
      teacherSchedulesCount: teacherSchedules.length,
      calendarSchedulesCount: calendarSchedules.length,
      semesterSchedulesCount: semesterSchedules.length
    }
    
  } catch (error) {
    console.error('❌ Schedule database integration test failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Example of backend data structure
export const mockBackendScheduleData = {
  idTkb: 1,
  idBuoi: 1, // Morning session
  soTiet: 2,
  idPhong: 101,
  idMonHoc: 1,
  idHocKy: 1,
  idCanBo: 1,
  idLop: 1,
  idHinhThuc: 1, // LT
  thuHoc: 2, // Monday
  ngayHoc: '2024-12-16',
  ghiChu: 'Bài học đầu tiên',
  trangThai: true,
  // Additional fields from joins
  tenMonHoc: 'Lập trình Java',
  tenLop: 'CNTT01',
  tenCanBo: 'Nguyễn Văn A',
  tenPhong: 'P101'
}

// Example of converted frontend data
export const convertedFrontendScheduleData: Schedule = {
  id: 1,
  subject: 'Lập trình Java',
  class: 'CNTT01',
  teacher: 'Nguyễn Văn A',
  room: 'P101',
  dayOfWeek: 'Thứ 2',
  startTime: '06:00',
  endTime: '12:00',
  scheduleDate: '2024-12-16',
  date: '2024-12-16',
  status: 'active',
  notes: 'Bài học đầu tiên',
  periods: 2,
  lessonType: 'LT',
  startDate: '2024-12-16',
  endDate: '2024-12-16',
  color: 'bg-blue-500'
}

// Demonstration of data conversion
export function demonstrateDataConversion() {
  console.log(`
📊 Schedule Database Integration Summary:

1. **API Methods Added:**
   - getAllSchedules() - Get all schedules with pagination
   - getSchedulesByTeacher() - Get schedules for specific teacher
   - getSchedulesBySemester() - Get schedules by semester
   - getSchedulesForCalendar() - Get schedules for calendar date range

2. **Data Conversion:**
   Backend (Vietnamese) → Frontend (English)
   - idTkb → id
   - tenMonHoc → subject
   - tenLop → class
   - tenCanBo → teacher
   - tenPhong → room
   - thuHoc → dayOfWeek (with Vietnamese day names)
   - idBuoi → startTime/endTime (session mapping)
   - ngayHoc → scheduleDate/date
   - trangThai → status (boolean → 'active'/'pending')

3. **Integration Points:**
   - ScheduleCalendar: Loads schedules by date range
   - Admin Page: Loads all schedules with pagination
   - Teacher Page: Loads teacher-specific schedules
   - Form Integration: Creates/updates with proper conversion

4. **Features:**
   - Loading states and error handling
   - Automatic refresh on filter changes
   - Color coding for visual display
   - Backward compatibility with existing interfaces

5. **Usage in Components:**
   - Calendar view: Real-time schedule display
   - List view: Paginated schedule management
   - Form integration: Create/edit with API calls
   - Teacher dashboard: Personal schedule view
  `)
}

// Test data for different scenarios
export const testScenarios = {
  emptyResponse: {
    content: [],
    totalElements: 0,
    totalPages: 0,
    size: 10,
    number: 0,
    first: true,
    last: true,
    empty: true
  },
  
  paginatedResponse: {
    content: [mockBackendScheduleData],
    totalElements: 1,
    totalPages: 1,
    size: 10,
    number: 0,
    first: true,
    last: true,
    empty: false
  },
  
  directArrayResponse: [mockBackendScheduleData],
  
  errorResponse: {
    error: 'Database connection failed',
    message: 'Unable to fetch schedules'
  }
}

// Helper function to simulate API responses
export function simulateApiResponse(scenario: keyof typeof testScenarios) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const response = testScenarios[scenario]
      if (scenario === 'errorResponse') {
        reject(new Error(response.message))
      } else {
        resolve(response)
      }
    }, 1000) // Simulate network delay
  })
}
