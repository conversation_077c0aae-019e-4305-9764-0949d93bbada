"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Plus, Edit, Trash2, BookOpen, Search } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { masterDataService, Subject, Department } from "@/lib/services/masterDataService"

export default function SubjectsPage() {
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [formData, setFormData] = useState({
    maMonHoc: "",
    tenMonHoc: "",
    soTinChi: 3,
    soTietLyThuyet: 30,
    soTietThucHanh: 15,
    moTa: "",
    idKhoa: 0,
    trangThai: true
  })

  useEffect(() => {
    fetchSubjects()
    fetchDepartments()
  }, [])

  const fetchSubjects = async () => {
    try {
      setLoading(true)
      const response = await masterDataService.getSubjects(0, 100, searchTerm)
      setSubjects(response.content || [])
    } catch (error) {
      console.error('Error fetching subjects:', error)
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách môn học",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchDepartments = async () => {
    try {
      const depts = await masterDataService.getAllDepartments()
      setDepartments(depts)
    } catch (error) {
      console.error('Error fetching departments:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (formData.idKhoa === 0) {
      toast({
        title: "Lỗi",
        description: "Vui lòng chọn khoa",
        variant: "destructive"
      })
      return
    }

    try {
      if (editingSubject) {
        await masterDataService.updateSubject(editingSubject.id!, formData)
        toast({
          title: "Thành công",
          description: "Cập nhật môn học thành công"
        })
      } else {
        await masterDataService.createSubject(formData)
        toast({
          title: "Thành công",
          description: "Tạo môn học mới thành công"
        })
      }
      setDialogOpen(false)
      resetForm()
      fetchSubjects()
    } catch (error) {
      console.error('Error saving subject:', error)
      toast({
        title: "Lỗi",
        description: "Không thể lưu thông tin môn học",
        variant: "destructive"
      })
    }
  }

  const handleEdit = (subject: Subject) => {
    setEditingSubject(subject)
    setFormData({
      maMonHoc: subject.maMonHoc,
      tenMonHoc: subject.tenMonHoc,
      soTinChi: subject.soTinChi,
      soTietLyThuyet: subject.soTietLyThuyet,
      soTietThucHanh: subject.soTietThucHanh,
      moTa: subject.moTa || "",
      idKhoa: subject.idKhoa,
      trangThai: subject.trangThai
    })
    setDialogOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (!confirm('Bạn có chắc chắn muốn xóa môn học này?')) {
      return
    }

    try {
      await masterDataService.deleteSubject(id)
      toast({
        title: "Thành công",
        description: "Xóa môn học thành công"
      })
      fetchSubjects()
    } catch (error) {
      console.error('Error deleting subject:', error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa môn học",
        variant: "destructive"
      })
    }
  }

  const resetForm = () => {
    setFormData({
      maMonHoc: "",
      tenMonHoc: "",
      soTinChi: 3,
      soTietLyThuyet: 30,
      soTietThucHanh: 15,
      moTa: "",
      idKhoa: 0,
      trangThai: true
    })
    setEditingSubject(null)
  }

  const handleSearch = () => {
    fetchSubjects()
  }

  const getDepartmentName = (idKhoa: number) => {
    const dept = departments.find(d => d.id === idKhoa)
    return dept?.tenKhoa || 'N/A'
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Quản Lý Môn Học</h1>
          <p className="text-muted-foreground">
            Quản lý danh sách môn học và chương trình đào tạo
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="mr-2 h-4 w-4" />
              Thêm Môn Học
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {editingSubject ? "Cập nhật môn học" : "Thêm môn học mới"}
              </DialogTitle>
              <DialogDescription>
                Nhập thông tin môn học. Nhấn lưu khi hoàn tất.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="maMonHoc" className="text-right">
                    Mã môn học *
                  </Label>
                  <Input
                    id="maMonHoc"
                    value={formData.maMonHoc}
                    onChange={(e) => setFormData({...formData, maMonHoc: e.target.value})}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="tenMonHoc" className="text-right">
                    Tên môn học *
                  </Label>
                  <Input
                    id="tenMonHoc"
                    value={formData.tenMonHoc}
                    onChange={(e) => setFormData({...formData, tenMonHoc: e.target.value})}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="idKhoa" className="text-right">
                    Khoa *
                  </Label>
                  <Select
                    value={formData.idKhoa.toString()}
                    onValueChange={(value) => setFormData({...formData, idKhoa: parseInt(value)})}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Chọn khoa" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id!.toString()}>
                          {dept.tenKhoa}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="soTinChi" className="text-right">
                    Số tín chỉ *
                  </Label>
                  <Input
                    id="soTinChi"
                    type="number"
                    min="1"
                    max="10"
                    value={formData.soTinChi}
                    onChange={(e) => setFormData({...formData, soTinChi: parseInt(e.target.value)})}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="soTietLyThuyet" className="text-right">
                    Tiết lý thuyết
                  </Label>
                  <Input
                    id="soTietLyThuyet"
                    type="number"
                    min="0"
                    value={formData.soTietLyThuyet}
                    onChange={(e) => setFormData({...formData, soTietLyThuyet: parseInt(e.target.value)})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="soTietThucHanh" className="text-right">
                    Tiết thực hành
                  </Label>
                  <Input
                    id="soTietThucHanh"
                    type="number"
                    min="0"
                    value={formData.soTietThucHanh}
                    onChange={(e) => setFormData({...formData, soTietThucHanh: parseInt(e.target.value)})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="moTa" className="text-right">
                    Mô tả
                  </Label>
                  <Input
                    id="moTa"
                    value={formData.moTa}
                    onChange={(e) => setFormData({...formData, moTa: e.target.value})}
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">Lưu</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Tìm kiếm</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="Tìm kiếm theo tên hoặc mã môn học..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <Button onClick={handleSearch}>
              <Search className="mr-2 h-4 w-4" />
              Tìm kiếm
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Subjects Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BookOpen className="mr-2 h-5 w-5" />
            Danh sách môn học ({subjects.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">Đang tải...</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Mã môn học</TableHead>
                  <TableHead>Tên môn học</TableHead>
                  <TableHead>Khoa</TableHead>
                  <TableHead>Tín chỉ</TableHead>
                  <TableHead>Tiết học</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {subjects.map((subject) => (
                  <TableRow key={subject.id}>
                    <TableCell className="font-medium">{subject.maMonHoc}</TableCell>
                    <TableCell>{subject.tenMonHoc}</TableCell>
                    <TableCell>{getDepartmentName(subject.idKhoa)}</TableCell>
                    <TableCell>{subject.soTinChi}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>LT: {subject.soTietLyThuyet}</div>
                        <div>TH: {subject.soTietThucHanh}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={subject.trangThai ? "default" : "secondary"}>
                        {subject.trangThai ? "Hoạt động" : "Không hoạt động"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(subject)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(subject.id!)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
