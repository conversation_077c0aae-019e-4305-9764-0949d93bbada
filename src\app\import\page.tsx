'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  ArrowLeft,
  Upload,
  Download,
  RefreshCw,
  Database,
  FileSpreadsheet,
  CheckCircle,
  AlertCircle,
  Clock,
  Users,
  BookOpen,
  MapPin,
  Calendar
} from 'lucide-react'

interface ImportStatus {
  type: string
  status: 'pending' | 'success' | 'error' | 'syncing'
  count: number
  lastSync: string
  message?: string
}

export default function ImportPage() {
  const [importStatuses, setImportStatuses] = useState<ImportStatus[]>([
    {
      type: 'subjects',
      status: 'success',
      count: 45,
      lastSync: '2024-01-15 09:30:00',
      message: '<PERSON><PERSON><PERSON> bộ thành công'
    },
    {
      type: 'classes',
      status: 'success', 
      count: 28,
      lastSync: '2024-01-15 09:30:00',
      message: '<PERSON><PERSON>ng bộ thành công'
    },
    {
      type: 'teachers',
      status: 'pending',
      count: 0,
      lastSync: '',
      message: 'Chưa đồng bộ'
    },
    {
      type: 'rooms',
      status: 'success',
      count: 35,
      lastSync: '2024-01-14 14:20:00',
      message: 'Đồng bộ thành công'
    },
    {
      type: 'academic_years',
      status: 'success',
      count: 3,
      lastSync: '2024-01-10 10:00:00',
      message: 'Đồng bộ thành công'
    }
  ])

  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState<'success' | 'error'>('success')
  const router = useRouter()

  useEffect(() => {
    // Kiểm tra token
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }
  }, [router])

  const getTypeInfo = (type: string) => {
    switch (type) {
      case 'subjects':
        return { name: 'Môn học', icon: BookOpen, color: 'bg-blue-500' }
      case 'classes':
        return { name: 'Lớp học', icon: Users, color: 'bg-green-500' }
      case 'teachers':
        return { name: 'Giảng viên', icon: Users, color: 'bg-purple-500' }
      case 'rooms':
        return { name: 'Phòng học', icon: MapPin, color: 'bg-orange-500' }
      case 'academic_years':
        return { name: 'Năm học/Học kỳ', icon: Calendar, color: 'bg-indigo-500' }
      default:
        return { name: type, icon: Database, color: 'bg-gray-500' }
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case 'syncing':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const handleSync = async (type: string) => {
    setLoading(true)
    setMessage('')

    // Update status to syncing
    setImportStatuses(prev => 
      prev.map(item => 
        item.type === type 
          ? { ...item, status: 'syncing' as const, message: 'Đang đồng bộ...' }
          : item
      )
    )

    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`http://localhost:8080/api/import/sync/${type}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        // Update status to success
        setImportStatuses(prev => 
          prev.map(item => 
            item.type === type 
              ? { 
                  ...item, 
                  status: 'success' as const, 
                  count: data.data.count || 0,
                  lastSync: new Date().toLocaleString('vi-VN'),
                  message: 'Đồng bộ thành công'
                }
              : item
          )
        )
        setMessage(`Đồng bộ ${getTypeInfo(type).name} thành công!`)
        setMessageType('success')
      } else {
        // Update status to error
        setImportStatuses(prev => 
          prev.map(item => 
            item.type === type 
              ? { ...item, status: 'error' as const, message: data.message || 'Lỗi đồng bộ' }
              : item
          )
        )
        setMessage(data.message || 'Có lỗi xảy ra khi đồng bộ')
        setMessageType('error')
      }
    } catch (error) {
      console.error('Error syncing:', error)
      setImportStatuses(prev => 
        prev.map(item => 
          item.type === type 
            ? { ...item, status: 'error' as const, message: 'Lỗi kết nối' }
            : item
        )
      )
      setMessage('Có lỗi xảy ra khi đồng bộ')
      setMessageType('error')
    } finally {
      setLoading(false)
    }
  }

  const handleSyncAll = async () => {
    setLoading(true)
    setMessage('')

    for (const item of importStatuses) {
      await handleSync(item.type)
      // Delay between syncs
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    setLoading(false)
    setMessage('Đồng bộ tất cả dữ liệu thành công!')
    setMessageType('success')
  }

  const handleFileUpload = async (type: string, file: File) => {
    setLoading(true)
    setMessage('')

    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)

    try {
      const token = localStorage.getItem('token')
      const response = await fetch('http://localhost:8080/api/import/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      })

      const data = await response.json()

      if (data.success) {
        setMessage(`Import file ${getTypeInfo(type).name} thành công!`)
        setMessageType('success')
        // Refresh status
        await handleSync(type)
      } else {
        setMessage(data.message || 'Có lỗi xảy ra khi import file')
        setMessageType('error')
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      setMessage('Có lỗi xảy ra khi import file')
      setMessageType('error')
    } finally {
      setLoading(false)
    }
  }

  const downloadTemplate = (type: string) => {
    const templates = {
      subjects: 'ma_mon_hoc,ten_mon_hoc,so_tin_chi,loai_mon\nIT001,Lập trình Java,3,Bắt buộc\nIT002,Cơ sở dữ liệu,3,Bắt buộc',
      classes: 'ma_lop,ten_lop,khoa,khoa_hoc,si_so\nCNTT01,Công nghệ thông tin 01,CNTT,2023,35\nCNTT02,Công nghệ thông tin 02,CNTT,2023,32',
      teachers: 'ma_can_bo,ten_can_bo,email,so_dien_thoai,khoa,chuc_vu\nGV001,Nguyễn Văn A,<EMAIL>,0123456789,CNTT,Giảng viên\nGV002,Trần Thị B,<EMAIL>,0987654321,CNTT,Phó giáo sư',
      rooms: 'ma_phong,ten_phong,loai_phong,suc_chua,co_so\nA101,Phòng A101,Lý thuyết,50,Cơ sở 1\nB201,Phòng B201,Thực hành,30,Cơ sở 1',
      academic_years: 'nam_hoc,hoc_ky,ngay_bat_dau,ngay_ket_thuc,trang_thai\n2023-2024,1,2023-09-01,2024-01-15,Đã kết thúc\n2023-2024,2,2024-01-16,2024-05-30,Đang diễn ra'
    }

    const content = templates[type as keyof typeof templates] || ''
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `template_${type}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => router.push('/dashboard')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại
              </Button>
              <Database className="h-6 w-6 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">
                Import & Đồng bộ dữ liệu
              </h1>
            </div>
            <Button onClick={handleSyncAll} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Đồng bộ tất cả
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {message && (
          <Alert variant={messageType === 'error' ? 'destructive' : 'default'} className="mb-6">
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        {/* Overview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Tổng quan dữ liệu</CardTitle>
            <CardDescription>
              Quản lý và đồng bộ dữ liệu từ các hệ thống khác
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {importStatuses.filter(s => s.status === 'success').length}
                </div>
                <div className="text-sm text-gray-600">Đã đồng bộ</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {importStatuses.filter(s => s.status === 'pending').length}
                </div>
                <div className="text-sm text-gray-600">Chờ đồng bộ</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {importStatuses.reduce((sum, s) => sum + s.count, 0)}
                </div>
                <div className="text-sm text-gray-600">Tổng bản ghi</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Sources */}
        <div className="grid gap-6">
          {importStatuses.map((item) => {
            const typeInfo = getTypeInfo(item.type)
            const IconComponent = typeInfo.icon

            return (
              <Card key={item.type} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 ${typeInfo.color} rounded-lg flex items-center justify-center`}>
                        <IconComponent className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{typeInfo.name}</CardTitle>
                        <CardDescription>
                          {item.count > 0 ? `${item.count} bản ghi` : 'Chưa có dữ liệu'}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(item.status)}
                      <span className="text-sm text-gray-600">
                        {item.lastSync ? new Date(item.lastSync).toLocaleString('vi-VN') : 'Chưa đồng bộ'}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      {item.message}
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadTemplate(item.type)}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Template
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => document.getElementById(`file-${item.type}`)?.click()}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Import File
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleSync(item.type)}
                        disabled={loading || item.status === 'syncing'}
                      >
                        <RefreshCw className={`h-4 w-4 mr-2 ${item.status === 'syncing' ? 'animate-spin' : ''}`} />
                        Đồng bộ
                      </Button>
                      <input
                        id={`file-${item.type}`}
                        type="file"
                        accept=".csv,.xlsx,.xls"
                        className="hidden"
                        onChange={(e) => {
                          const file = e.target.files?.[0]
                          if (file) {
                            handleFileUpload(item.type, file)
                          }
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Instructions */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Hướng dẫn sử dụng</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <strong>1. Đồng bộ tự động:</strong> Click "Đồng bộ" để kết nối với hệ thống nguồn và tự động import dữ liệu.
              </div>
              <div>
                <strong>2. Import file:</strong> Tải template CSV, điền dữ liệu và upload lên hệ thống.
              </div>
              <div>
                <strong>3. Định dạng file:</strong> Hỗ trợ CSV, Excel (.xlsx, .xls) với encoding UTF-8.
              </div>
              <div>
                <strong>4. Thứ tự import:</strong> Nên import theo thứ tự: Năm học → Môn học → Giảng viên → Phòng học → Lớp học.
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
