"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Users,
  BookOpen,
  GraduationCap,
  Building,
  Calendar,
  Database,
  Download,
  Upload,
  Settings,
  BarChart3,
  RefreshCw,
  History,
  ArrowRight,
  Clock,
  FileText,
  User
} from "lucide-react"
import StatsCard from '@/components/dashboard/StatsCard'
import QuickActionCard from '@/components/dashboard/QuickActionCard'
import { adminService, DashboardStats } from "@/lib/services/adminService"
import { handleAsync } from '@/lib/utils/errorHandler'

export default function AdminPage() {
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setLoading(true)

    const { data, error } = await handleAsync(
      () => adminService.getDashboardData(),
      undefined,
      'Loading dashboard data'
    )

    if (data) {
      setStats(data)
    }

    if (error) {
      // Error đã được log trong handleAsync, chỉ cần hiển thị thông báo nếu cần
      console.warn('Dashboard data loaded with fallback')
    }

    setLoading(false)
  }

  const handleBackup = async () => {
    try {
      const backupPath = await adminService.createBackup()
      alert(`Sao lưu thành công: ${backupPath}`)
    } catch (error) {
      alert('Lỗi khi tạo sao lưu')
    }
  }

  const handleInitializeData = async () => {
    if (confirm('Bạn có chắc chắn muốn khởi tạo lại dữ liệu? Điều này sẽ xóa tất cả dữ liệu hiện tại.')) {
      try {
        await adminService.initializeData()
        alert('Khởi tạo dữ liệu thành công')
        loadDashboardData()
      } catch (error) {
        alert('Lỗi khi khởi tạo dữ liệu')
      }
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dashboard Quản Trị</h1>
          <p className="text-muted-foreground">
            Quản lý hệ thống lịch giảng và dữ liệu danh mục
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={handleBackup} variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Sao Lưu
          </Button>
          <Button onClick={handleInitializeData} variant="outline" size="sm">
            <Database className="mr-2 h-4 w-4" />
            Khởi Tạo
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Tổng Giảng Viên"
          value={stats?.totalTeachers || 0}
          description="Đang hoạt động"
          icon={Users}
        />
        <StatsCard
          title="Tổng Lịch Giảng"
          value={stats?.totalSchedules || 0}
          description="Trong hệ thống"
          icon={Calendar}
        />
        <StatsCard
          title="Tổng Môn Học"
          value={stats?.totalSubjects || 0}
          description="Đã đăng ký"
          icon={BookOpen}
        />
        <StatsCard
          title="Tổng Lớp Học"
          value={stats?.totalClasses || 0}
          description="Đang hoạt động"
          icon={GraduationCap}
        />
      </div>

      {/* Current Semester Info */}
      {stats?.currentSemester && (
        <Card>
          <CardHeader>
            <CardTitle>Học Kỳ Hiện Tại</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant="default">{stats.currentSemester}</Badge>
              <span className="text-sm text-muted-foreground">
                {stats.weeklySchedules} lịch giảng trong tuần này
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Management Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Tổng Quan</TabsTrigger>
          <TabsTrigger value="academic">Năm Học & Học Kỳ</TabsTrigger>
          <TabsTrigger value="master-data">Dữ Liệu Danh Mục</TabsTrigger>
          <TabsTrigger value="import-export">Import/Export</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <QuickActionCard
              title="Năm Học & Học Kỳ"
              description="Quản lý năm học, học kỳ và thiết lập thời gian"
              icon={Calendar}
              href="/admin/academic-years"
              color="bg-blue-500"
            />
            <QuickActionCard
              title="Quản Lý Lịch Giảng"
              description="Sắp xếp và quản lý lịch giảng cho toàn trường"
              icon={Calendar}
              href="/admin/schedules"
              color="bg-green-500"
            />
            <QuickActionCard
              title="Dữ Liệu Danh Mục"
              description="Quản lý khoa, môn học, lớp học, phòng học, giảng viên"
              icon={Database}
              href="/admin/master-data"
              color="bg-blue-500"
            />
            <QuickActionCard
              title="Import & Đồng Bộ"
              description="Import dữ liệu từ Excel và đồng bộ hệ thống"
              icon={Upload}
              href="/admin/import-management"
              color="bg-purple-500"
            />
            <QuickActionCard
              title="Thống Kê & Báo Cáo"
              description="Dashboard, thống kê và báo cáo hệ thống"
              icon={BarChart3}
              href="/admin/reports"
              color="bg-orange-500"
            />
            <QuickActionCard
              title="Sao Lưu & Khôi Phục"
              description="Quản lý backup và khôi phục dữ liệu"
              icon={Download}
              href="/admin/backup"
              color="bg-red-500"
            />
            <QuickActionCard
              title="Cài Đặt Hệ Thống"
              description="Cấu hình hệ thống và tham số quản trị"
              icon={Settings}
              href="/admin/settings"
              color="bg-gray-500"
            />
          </div>
        </TabsContent>

        <TabsContent value="academic" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Năm Học</CardTitle>
                <CardDescription>
                  Quản lý các năm học/niên khóa
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full"
                  onClick={() => router.push('/admin/academic-years')}
                >
                  Quản Lý Năm Học
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Học Kỳ</CardTitle>
                <CardDescription>
                  Quản lý các học kỳ trong năm
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full"
                  onClick={() => router.push('/admin/semesters')}
                >
                  Quản Lý Học Kỳ
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="master-data" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <QuickActionCard
              title="Khoa/Phòng Ban"
              description="Quản lý thông tin các khoa và phòng ban"
              icon={Building}
              href="/admin/master-data/departments"
              color="bg-blue-500"
            />
            <QuickActionCard
              title="Môn Học"
              description="Quản lý danh sách môn học và chương trình"
              icon={BookOpen}
              href="/admin/master-data/subjects"
              color="bg-green-500"
            />
            <QuickActionCard
              title="Lớp Học"
              description="Quản lý thông tin lớp học và sinh viên"
              icon={GraduationCap}
              href="/admin/master-data/classes"
              color="bg-purple-500"
            />
            <QuickActionCard
              title="Phòng Học"
              description="Quản lý phòng học và trang thiết bị"
              icon={Building}
              href="/admin/master-data/rooms"
              color="bg-orange-500"
            />
            <QuickActionCard
              title="Giảng Viên"
              description="Quản lý thông tin giảng viên và chuyên môn"
              icon={User}
              href="/admin/master-data/teachers"
              color="bg-red-500"
            />
            <QuickActionCard
              title="Cơ Sở"
              description="Quản lý các cơ sở và địa điểm giảng dạy"
              icon={Building}
              href="/admin/master-data/campuses"
              color="bg-indigo-500"
            />
          </div>
        </TabsContent>

        <TabsContent value="import-export" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => router.push('/admin/import-management')}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="mr-2 h-5 w-5" />
                  Quản lý Import & Đồng bộ
                </CardTitle>
                <CardDescription>
                  Giao diện quản lý CRUD đầy đủ cho import và đồng bộ dữ liệu
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center text-sm text-gray-600">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Đồng bộ tự động từ hệ thống nguồn
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload file Excel/CSV
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <History className="mr-2 h-4 w-4" />
                  Lịch sử import chi tiết
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Settings className="mr-2 h-4 w-4" />
                  Cấu hình đồng bộ
                </div>
                <Button className="w-full mt-4">
                  <ArrowRight className="mr-2 h-4 w-4" />
                  Mở Quản lý Import
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Download className="mr-2 h-5 w-5" />
                  Export Template
                </CardTitle>
                <CardDescription>
                  Tải template Excel để import dữ liệu
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Khoa
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Môn Học
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Lớp Học
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Giảng Viên
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
