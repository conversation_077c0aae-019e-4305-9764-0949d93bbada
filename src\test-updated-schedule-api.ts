// Test file for updated schedule API integration
import { scheduleService, CalendarEvent, Schedule } from '@/lib/services/scheduleService'

// Example usage of the updated schedule API
export async function testUpdatedScheduleAPI() {
  try {
    console.log('🧪 Testing Updated Schedule API Integration...')
    
    // Test 1: Get calendar events for current month
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)
    
    const startDate = startOfMonth.toISOString().split('T')[0]
    const endDate = endOfMonth.toISOString().split('T')[0]
    
    console.log(`📅 Fetching calendar events from ${startDate} to ${endDate}`)
    const calendarEvents = await scheduleService.getCalendarEvents({
      startDate,
      endDate,
      semesterId: 1
    })
    console.log('✅ Calendar events loaded:', calendarEvents)
    
    // Test 2: Get schedules for calendar (converted format)
    console.log('📊 Fetching schedules for calendar view...')
    const schedules = await scheduleService.getSchedulesForCalendar({
      startDate,
      endDate,
      semesterId: 1
    })
    console.log('✅ Schedules loaded:', schedules)
    
    // Test 3: Get teacher schedules
    console.log('👨‍🏫 Fetching teacher schedules for teacher ID: 123')
    const teacherSchedules = await scheduleService.getSchedulesByTeacher(123, 1)
    console.log('✅ Teacher schedules loaded:', teacherSchedules)
    
    // Test 4: Get all schedules
    console.log('📋 Fetching all schedules...')
    const allSchedules = await scheduleService.getAllSchedules({
      semesterId: 1,
      departmentId: 1
    })
    console.log('✅ All schedules loaded:', allSchedules)
    
    return {
      success: true,
      calendarEventsCount: calendarEvents.length,
      schedulesCount: schedules.length,
      teacherSchedulesCount: teacherSchedules.length,
      allSchedulesCount: allSchedules.length
    }
    
  } catch (error) {
    console.error('❌ Updated schedule API test failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Mock calendar event data based on API documentation
export const mockCalendarEventData: CalendarEvent = {
  id: 1,
  title: "JAVA001 - P.101",
  description: "Môn: Lập trình Java\nGV: Nguyễn Văn A\nLớp: CNTT2021A\nPhòng: P.101 (Cơ sở 1)\nBuổi: Sáng\nHình thức: Lý thuyết\nSố tiết: 3",
  start: "2025-06-02T07:00:00",
  end: "2025-06-02T09:30:00",
  allDay: false,
  backgroundColor: "#3788d8",
  borderColor: "#3788d8",
  textColor: "#ffffff",
  extendedProps: {
    scheduleId: 1,
    teacherId: 123,
    teacherName: "Nguyễn Văn A",
    subjectCode: "JAVA001",
    subjectName: "Lập trình Java",
    classCode: "CNTT2021A",
    className: "Công nghệ thông tin 2021 A",
    roomName: "P.101",
    campusName: "Cơ sở 1",
    sessionName: "Sáng",
    formType: "Lý thuyết",
    practiceGroup: null,
    credits: 3,
    coefficient: 1.0,
    note: null
  }
}

// Expected converted schedule data
export const expectedScheduleData: Schedule = {
  id: 1,
  subject: "Lập trình Java",
  class: "Công nghệ thông tin 2021 A",
  teacher: "Nguyễn Văn A",
  room: "P.101",
  dayOfWeek: "Thứ 2", // Converted from date
  startTime: "07:00",
  endTime: "09:30",
  scheduleDate: "2025-06-02",
  date: "2025-06-02",
  status: "active",
  notes: "",
  periods: 3,
  lessonType: "LT",
  color: "#3788d8",
  startDate: "2025-06-02",
  endDate: "2025-06-02",
  // Additional fields from API
  subjectCode: "JAVA001",
  classCode: "CNTT2021A",
  campusName: "Cơ sở 1",
  sessionName: "Sáng",
  practiceGroup: undefined,
  credits: 3,
  coefficient: 1.0
}

// API request examples
export const apiRequestExamples = {
  // Get all schedules for current month
  getAllSchedules: {
    endpoint: '/api/schedules/calendar',
    params: {
      startDate: '2025-05-31',
      endDate: '2025-06-29',
      semesterId: 1
    }
  },
  
  // Get teacher schedules
  getTeacherSchedules: {
    endpoint: '/api/schedules/calendar',
    params: {
      startDate: '2025-05-31',
      endDate: '2025-06-29',
      semesterId: 1,
      teacherId: 123
    }
  },
  
  // Get class schedules
  getClassSchedules: {
    endpoint: '/api/schedules/calendar',
    params: {
      startDate: '2025-05-31',
      endDate: '2025-06-29',
      semesterId: 1,
      classId: 456
    }
  },
  
  // Get department schedules
  getDepartmentSchedules: {
    endpoint: '/api/schedules/calendar',
    params: {
      startDate: '2025-05-31',
      endDate: '2025-06-29',
      semesterId: 1,
      departmentId: 789
    }
  }
}

// Color mapping from API documentation
export const colorMapping = {
  'Lý thuyết': '#3788d8',
  'Thực hành': '#28a745',
  'Bài tập': '#ffc107',
  'Thực nghiệm': '#dc3545',
  'Đồ án': '#6f42c1',
  'Khác': '#6c757d'
}

// Demonstration function
export function demonstrateAPIUpdate() {
  console.log(`
📊 Updated Schedule API Integration Summary:

1. **New API Endpoint:**
   - Primary: GET /api/schedules/calendar
   - Replaces multiple endpoints with unified calendar API

2. **Enhanced Data Structure:**
   - Rich calendar event format with extendedProps
   - Automatic color coding by lesson type
   - Complete schedule metadata

3. **Improved Parameters:**
   - startDate & endDate (required)
   - semesterId, teacherId, classId, departmentId (optional)
   - Role-based automatic filtering

4. **Data Conversion:**
   CalendarEvent → Schedule conversion for UI compatibility
   - Preserves all original data
   - Adds computed fields (dayOfWeek, etc.)
   - Maintains color information

5. **Benefits:**
   - Standardized API format
   - Better performance with date range filtering
   - Rich metadata for advanced features
   - Automatic security filtering
   - Visual color coding

6. **Usage Examples:**
   - Calendar views: getSchedulesForCalendar()
   - Teacher dashboard: getSchedulesByTeacher()
   - Admin management: getAllSchedules()
   - Raw events: getCalendarEvents()
  `)
}

// Test different scenarios
export const testScenarios = {
  // Normal response
  successResponse: {
    success: true,
    message: "Lấy lịch giảng calendar thành công",
    data: [mockCalendarEventData]
  },
  
  // Empty response
  emptyResponse: {
    success: true,
    message: "Không có lịch giảng trong khoảng thời gian này",
    data: []
  },
  
  // Error response
  errorResponse: {
    success: false,
    message: "Ngày bắt đầu không được sau ngày kết thúc",
    data: null
  },
  
  // Unauthorized response
  unauthorizedResponse: {
    success: false,
    message: "Unauthorized",
    data: null
  }
}
