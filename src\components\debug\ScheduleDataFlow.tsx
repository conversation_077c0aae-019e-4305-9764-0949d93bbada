'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { scheduleService } from '@/lib/services/scheduleService'

export default function ScheduleDataFlow() {
  const [schedules, setSchedules] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [step, setStep] = useState(0)

  const testDataFlow = async () => {
    try {
      setLoading(true)
      setStep(1)
      
      console.log('🧪 Step 1: Testing API call...')
      
      const params = {
        startDate: '2025-06-01',
        endDate: '2025-06-30',
        semesterId: 1
      }
      
      setStep(2)
      console.log('🧪 Step 2: Calling getSchedulesForCalendar...')
      
      const result = await scheduleService.getSchedulesForCalendar(params)
      
      setStep(3)
      console.log('🧪 Step 3: Got result:', result)
      console.log('🧪 Result type:', typeof result)
      console.log('🧪 Is array:', Array.isArray(result))
      console.log('🧪 Length:', result?.length)
      
      setStep(4)
      console.log('🧪 Step 4: Setting schedules state...')
      setSchedules(result)
      
      setStep(5)
      console.log('🧪 Step 5: Complete!')
      
    } catch (error) {
      console.error('❌ Test failed:', error)
      setStep(-1)
    } finally {
      setLoading(false)
    }
  }

  // Monitor schedules state changes
  useEffect(() => {
    console.log('📊 Schedules state changed:', schedules)
    console.log('📊 Schedules count:', schedules.length)
    if (schedules.length > 0) {
      console.log('📊 First schedule:', schedules[0])
      console.log('📊 Schedule dates:', schedules.map(s => s.date || s.scheduleDate))
    }
  }, [schedules])

  const getStepStatus = (stepNum: number) => {
    if (step === -1) return '❌'
    if (step >= stepNum) return '✅'
    if (step === stepNum - 1 && loading) return '⏳'
    return '⏸️'
  }

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>🔍 Schedule Data Flow Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={testDataFlow} 
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Testing Data Flow...' : 'Test Schedule Data Flow'}
          </Button>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-lg">{getStepStatus(1)}</span>
              <span>Step 1: Initialize API call</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-lg">{getStepStatus(2)}</span>
              <span>Step 2: Call getSchedulesForCalendar</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-lg">{getStepStatus(3)}</span>
              <span>Step 3: Receive API response</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-lg">{getStepStatus(4)}</span>
              <span>Step 4: Set schedules state</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-lg">{getStepStatus(5)}</span>
              <span>Step 5: Complete</span>
            </div>
          </div>

          {schedules.length > 0 && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-md">
              <h3 className="font-medium text-green-800 mb-2">
                ✅ Success! Found {schedules.length} schedule(s)
              </h3>
              <div className="space-y-2">
                {schedules.map((schedule, index) => (
                  <div key={index} className="text-sm text-green-700 bg-white p-2 rounded border">
                    <div><strong>Subject:</strong> {schedule.subject}</div>
                    <div><strong>Class:</strong> {schedule.class}</div>
                    <div><strong>Date:</strong> {schedule.date || schedule.scheduleDate}</div>
                    <div><strong>Time:</strong> {schedule.startTime} - {schedule.endTime}</div>
                    <div><strong>Room:</strong> {schedule.room}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {step === -1 && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <h3 className="font-medium text-red-800">❌ Test Failed</h3>
              <p className="text-red-600">Check console for detailed error logs.</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>📋 Debug Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>1. Open Browser Console</strong> (F12) before clicking test</p>
            <p><strong>2. Click "Test Schedule Data Flow"</strong></p>
            <p><strong>3. Watch console logs</strong> for each step:</p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>🚀 API call parameters</li>
              <li>📥 Raw API response</li>
              <li>🔄 Data conversion logs</li>
              <li>✅ Final converted schedules</li>
              <li>📊 State change monitoring</li>
            </ul>
            <p><strong>4. Check results</strong> in both console and UI</p>
            <p><strong>5. If successful</strong>, schedules should display in green box above</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
