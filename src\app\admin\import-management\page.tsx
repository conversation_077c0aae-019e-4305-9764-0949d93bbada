'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ArrowLeft,
  Database,
  Upload,
  Settings,
  History,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react'
import ImportDataTable from '@/components/admin/ImportDataTable'
import FileUploadDialog from '@/components/admin/FileUploadDialog'
import { importService, ImportResult } from '@/lib/services/importService'

const DATA_TYPE_NAMES = {
  subjects: 'Môn học',
  classes: 'Lớp học', 
  teachers: 'Giảng viên',
  rooms: '<PERSON><PERSON><PERSON> học',
  academic_years: '<PERSON><PERSON><PERSON> họ<PERSON>/<PERSON>ọ<PERSON> k<PERSON>'
}

export default function ImportManagementPage() {
  const router = useRouter()
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [selectedDataType, setSelectedDataType] = useState<string>('')
  const [connectionStatus, setConnectionStatus] = useState<boolean | null>(null)
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info'
    message: string
  } | null>(null)

  useEffect(() => {
    checkConnection()
  }, [])

  const checkConnection = async () => {
    try {
      const status = await importService.testConnection()
      setConnectionStatus(status)
    } catch (error) {
      setConnectionStatus(false)
    }
  }

  const handleUpload = (type: string) => {
    setSelectedDataType(type)
    setUploadDialogOpen(true)
  }

  const handleUploadSuccess = (result: ImportResult) => {
    setNotification({
      type: 'success',
      message: `Import thành công ${result.count} bản ghi ${DATA_TYPE_NAMES[result.type as keyof typeof DATA_TYPE_NAMES]}`
    })
    
    // Auto hide notification after 5 seconds
    setTimeout(() => {
      setNotification(null)
    }, 5000)
  }

  const handleSync = (type: string) => {
    setNotification({
      type: 'info',
      message: `Đang đồng bộ ${DATA_TYPE_NAMES[type as keyof typeof DATA_TYPE_NAMES]}...`
    })
  }

  const handleConfigure = (type: string) => {
    router.push(`/admin/import-management/config/${type}`)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => router.push('/admin')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại
              </Button>
              <Database className="h-6 w-6 text-blue-600 mr-3" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Quản lý Import & Đồng bộ
                </h1>
                <p className="text-sm text-gray-500">
                  Quản lý việc import và đồng bộ dữ liệu từ hệ thống bên ngoài
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {/* Connection Status */}
              <div className="flex items-center space-x-2">
                {connectionStatus === true && (
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    <span className="text-sm">Kết nối OK</span>
                  </div>
                )}
                {connectionStatus === false && (
                  <div className="flex items-center text-red-600">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    <span className="text-sm">Mất kết nối</span>
                  </div>
                )}
                {connectionStatus === null && (
                  <div className="flex items-center text-gray-500">
                    <Info className="w-4 h-4 mr-1" />
                    <span className="text-sm">Đang kiểm tra...</span>
                  </div>
                )}
              </div>
              <Button onClick={checkConnection} variant="outline" size="sm">
                Kiểm tra kết nối
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Notification */}
        {notification && (
          <Alert 
            className={`mb-6 ${
              notification.type === 'success' ? 'border-green-200 bg-green-50' :
              notification.type === 'error' ? 'border-red-200 bg-red-50' :
              'border-blue-200 bg-blue-50'
            }`}
          >
            {notification.type === 'success' && <CheckCircle className="h-4 w-4 text-green-600" />}
            {notification.type === 'error' && <AlertCircle className="h-4 w-4 text-red-600" />}
            {notification.type === 'info' && <Info className="h-4 w-4 text-blue-600" />}
            <AlertDescription className={
              notification.type === 'success' ? 'text-green-800' :
              notification.type === 'error' ? 'text-red-800' :
              'text-blue-800'
            }>
              {notification.message}
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center">
              <Database className="w-4 h-4 mr-2" />
              Tổng quan
            </TabsTrigger>
            <TabsTrigger value="upload" className="flex items-center">
              <Upload className="w-4 h-4 mr-2" />
              Upload Files
            </TabsTrigger>
            <TabsTrigger value="config" className="flex items-center">
              <Settings className="w-4 h-4 mr-2" />
              Cấu hình
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center">
              <History className="w-4 h-4 mr-2" />
              Lịch sử
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <ImportDataTable
              onUpload={handleUpload}
              onSync={handleSync}
              onConfigure={handleConfigure}
            />
          </TabsContent>

          {/* Upload Tab */}
          <TabsContent value="upload" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {Object.entries(DATA_TYPE_NAMES).map(([type, name]) => (
                <Card key={type} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileSpreadsheet className="w-5 h-5 mr-2" />
                      {name}
                    </CardTitle>
                    <CardDescription>
                      Upload file Excel hoặc CSV để import dữ liệu {name.toLowerCase()}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button 
                      onClick={() => handleUpload(type)}
                      className="w-full"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Chọn file
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Config Tab */}
          <TabsContent value="config" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Cấu hình đồng bộ</CardTitle>
                <CardDescription>
                  Cấu hình kết nối và tham số đồng bộ dữ liệu
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {Object.entries(DATA_TYPE_NAMES).map(([type, name]) => (
                    <Button
                      key={type}
                      variant="outline"
                      onClick={() => handleConfigure(type)}
                      className="h-auto p-4 justify-start"
                    >
                      <Settings className="w-4 h-4 mr-3" />
                      <div className="text-left">
                        <div className="font-medium">{name}</div>
                        <div className="text-sm text-gray-500">Cấu hình đồng bộ {name.toLowerCase()}</div>
                      </div>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Lịch sử Import & Đồng bộ</CardTitle>
                <CardDescription>
                  Xem lịch sử các lần import và đồng bộ dữ liệu
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={() => router.push('/admin/import-management/history')}
                  className="w-full"
                >
                  <History className="w-4 h-4 mr-2" />
                  Xem lịch sử chi tiết
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      {/* Upload Dialog */}
      <FileUploadDialog
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
        dataType={selectedDataType}
        dataTypeName={DATA_TYPE_NAMES[selectedDataType as keyof typeof DATA_TYPE_NAMES] || ''}
        onSuccess={handleUploadSuccess}
      />
    </div>
  )
}
