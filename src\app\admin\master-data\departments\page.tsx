"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Plus, Edit, Trash2, Building, Search, Mail, Phone } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { masterDataService, Department } from "@/lib/services/masterDataService"

export default function DepartmentsPage() {
  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingDepartment, setEditingDepartment] = useState<Department | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [formData, setFormData] = useState({
    maKhoa: "",
    tenKhoa: "",
    moTa: "",
    email: "",
    soDienThoai: "",
    trangThai: true
  })

  useEffect(() => {
    fetchDepartments()
  }, [])

  const fetchDepartments = async () => {
    try {
      setLoading(true)
      const response = await masterDataService.getDepartments(0, 100, searchTerm)
      setDepartments(response.content || [])
    } catch (error) {
      console.error('Error fetching departments:', error)
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách khoa",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (editingDepartment) {
        await masterDataService.updateDepartment(editingDepartment.id!, formData)
        toast({
          title: "Thành công",
          description: "Cập nhật khoa thành công"
        })
      } else {
        await masterDataService.createDepartment(formData)
        toast({
          title: "Thành công",
          description: "Tạo khoa mới thành công"
        })
      }
      setDialogOpen(false)
      resetForm()
      fetchDepartments()
    } catch (error) {
      console.error('Error saving department:', error)
      toast({
        title: "Lỗi",
        description: "Không thể lưu thông tin khoa",
        variant: "destructive"
      })
    }
  }

  const handleEdit = (department: Department) => {
    setEditingDepartment(department)
    setFormData({
      maKhoa: department.maKhoa,
      tenKhoa: department.tenKhoa,
      moTa: department.moTa || "",
      email: department.email || "",
      soDienThoai: department.soDienThoai || "",
      trangThai: department.trangThai
    })
    setDialogOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (!confirm('Bạn có chắc chắn muốn xóa khoa này?')) {
      return
    }

    try {
      await masterDataService.deleteDepartment(id)
      toast({
        title: "Thành công",
        description: "Xóa khoa thành công"
      })
      fetchDepartments()
    } catch (error) {
      console.error('Error deleting department:', error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa khoa",
        variant: "destructive"
      })
    }
  }

  const resetForm = () => {
    setFormData({
      maKhoa: "",
      tenKhoa: "",
      moTa: "",
      email: "",
      soDienThoai: "",
      trangThai: true
    })
    setEditingDepartment(null)
  }

  const handleSearch = () => {
    fetchDepartments()
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Quản Lý Khoa</h1>
          <p className="text-muted-foreground">
            Quản lý thông tin các khoa và phòng ban
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="mr-2 h-4 w-4" />
              Thêm Khoa
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {editingDepartment ? "Cập nhật khoa" : "Thêm khoa mới"}
              </DialogTitle>
              <DialogDescription>
                Nhập thông tin khoa. Nhấn lưu khi hoàn tất.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="maKhoa" className="text-right">
                    Mã khoa *
                  </Label>
                  <Input
                    id="maKhoa"
                    value={formData.maKhoa}
                    onChange={(e) => setFormData({...formData, maKhoa: e.target.value})}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="tenKhoa" className="text-right">
                    Tên khoa *
                  </Label>
                  <Input
                    id="tenKhoa"
                    value={formData.tenKhoa}
                    onChange={(e) => setFormData({...formData, tenKhoa: e.target.value})}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">
                    Email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="soDienThoai" className="text-right">
                    Số điện thoại
                  </Label>
                  <Input
                    id="soDienThoai"
                    value={formData.soDienThoai}
                    onChange={(e) => setFormData({...formData, soDienThoai: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="moTa" className="text-right">
                    Mô tả
                  </Label>
                  <Input
                    id="moTa"
                    value={formData.moTa}
                    onChange={(e) => setFormData({...formData, moTa: e.target.value})}
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">Lưu</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Tìm kiếm</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="Tìm kiếm theo tên hoặc mã khoa..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <Button onClick={handleSearch}>
              <Search className="mr-2 h-4 w-4" />
              Tìm kiếm
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Departments Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building className="mr-2 h-5 w-5" />
            Danh sách khoa ({departments.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">Đang tải...</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Mã khoa</TableHead>
                  <TableHead>Tên khoa</TableHead>
                  <TableHead>Liên hệ</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {departments.map((department) => (
                  <TableRow key={department.id}>
                    <TableCell className="font-medium">{department.maKhoa}</TableCell>
                    <TableCell>{department.tenKhoa}</TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {department.email && (
                          <div className="flex items-center text-sm">
                            <Mail className="mr-1 h-3 w-3" />
                            {department.email}
                          </div>
                        )}
                        {department.soDienThoai && (
                          <div className="flex items-center text-sm">
                            <Phone className="mr-1 h-3 w-3" />
                            {department.soDienThoai}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={department.trangThai ? "default" : "secondary"}>
                        {department.trangThai ? "Hoạt động" : "Không hoạt động"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(department)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(department.id!)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
