'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function Home() {
  const router = useRouter()

  useEffect(() => {
    // Kiểm tra xem user đã đăng nhập chưa
    const token = localStorage.getItem('token')
    if (token) {
      // Kiểm tra vai trò để chuyển hướng phù hợp
      const userInfoStr = localStorage.getItem('userInfo')
      if (userInfoStr && userInfoStr !== 'undefined') {
        try {
          const userInfo = JSON.parse(userInfoStr)
          // Chuyển hướng dựa trên vai trò
          if (userInfo.vaiTro === 'Admin' || userInfo.vaiTro === 'ADMIN' || userInfo.vaiTro === 'QUAN_TRI') {
            router.push('/admin')
          } else {
            router.push('/teacher')
          }
        } catch (error) {
          console.error('Error parsing user info:', error)
          router.push('/login')
        }
      } else {
        router.push('/login')
      }
    } else {
      // Nếu chưa đăng nhập, chuyển đến trang login
      router.push('/login')
    }
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
    </div>
  )
}
