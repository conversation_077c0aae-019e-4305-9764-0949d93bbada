"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Plus, Edit, Trash2, Calendar, Star } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface Semester {
  idHocKy: number
  idNienKhoa: number
  tenHocKy: string
  soTuan: number
  ngayBatDau: string
  ngayKetThuc: string
  hienTai: boolean
  trangThai: boolean
  tenNienKhoa: string
  namNienKhoa: number
  soLichGiang: number
  trangThaiHocKy: string
  soNgayConLai: number
}

interface AcademicYear {
  idNienKhoa: number
  tenNienKhoa: string
  nam: number
}

interface SemesterForm {
  idNienKhoa: number
  tenHocKy: string
  soTuan: number
  ngayBatDau: string
  ngayKetThuc: string
  hienTai: boolean
  moTa: string
  trangThai: boolean
}

export default function SemestersPage() {
  const [semesters, setSemesters] = useState<Semester[]>([])
  const [academicYears, setAcademicYears] = useState<AcademicYear[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingSemester, setEditingSemester] = useState<Semester | null>(null)
  const [selectedAcademicYear, setSelectedAcademicYear] = useState<number | null>(null)
  const [formData, setFormData] = useState<SemesterForm>({
    idNienKhoa: 0,
    tenHocKy: "",
    soTuan: 16,
    ngayBatDau: "",
    ngayKetThuc: "",
    hienTai: false,
    moTa: "",
    trangThai: true
  })

  useEffect(() => {
    fetchAcademicYears()
    fetchSemesters()
  }, [])

  useEffect(() => {
    if (selectedAcademicYear) {
      fetchSemestersByAcademicYear(selectedAcademicYear)
    } else {
      fetchSemesters()
    }
  }, [selectedAcademicYear])

  const fetchAcademicYears = async () => {
    try {
      const response = await fetch('/api/academic-years/active', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setAcademicYears(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching academic years:', error)
    }
  }

  const fetchSemesters = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/semesters', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setSemesters(data.data.content || [])
      }
    } catch (error) {
      console.error('Error fetching semesters:', error)
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách học kỳ",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchSemestersByAcademicYear = async (academicYearId: number) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/semesters/academic-year/${academicYearId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setSemesters(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching semesters by academic year:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = editingSemester 
        ? `/api/semesters/${editingSemester.idHocKy}`
        : '/api/semesters'
      
      const method = editingSemester ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        toast({
          title: "Thành công",
          description: editingSemester ? "Cập nhật học kỳ thành công" : "Tạo học kỳ thành công"
        })
        setDialogOpen(false)
        resetForm()
        if (selectedAcademicYear) {
          fetchSemestersByAcademicYear(selectedAcademicYear)
        } else {
          fetchSemesters()
        }
      } else {
        const error = await response.json()
        toast({
          title: "Lỗi",
          description: error.message || "Có lỗi xảy ra",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error saving semester:', error)
      toast({
        title: "Lỗi",
        description: "Không thể lưu học kỳ",
        variant: "destructive"
      })
    }
  }

  const handleEdit = (semester: Semester) => {
    setEditingSemester(semester)
    setFormData({
      idNienKhoa: semester.idNienKhoa,
      tenHocKy: semester.tenHocKy,
      soTuan: semester.soTuan,
      ngayBatDau: semester.ngayBatDau,
      ngayKetThuc: semester.ngayKetThuc,
      hienTai: semester.hienTai,
      moTa: "",
      trangThai: semester.trangThai
    })
    setDialogOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (!confirm('Bạn có chắc chắn muốn xóa học kỳ này?')) {
      return
    }
    
    try {
      const response = await fetch(`/api/semesters/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        toast({
          title: "Thành công",
          description: "Xóa học kỳ thành công"
        })
        if (selectedAcademicYear) {
          fetchSemestersByAcademicYear(selectedAcademicYear)
        } else {
          fetchSemesters()
        }
      } else {
        const error = await response.json()
        toast({
          title: "Lỗi",
          description: error.message || "Không thể xóa học kỳ",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error deleting semester:', error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa học kỳ",
        variant: "destructive"
      })
    }
  }

  const handleSetCurrent = async (id: number) => {
    try {
      const response = await fetch(`/api/semesters/${id}/set-current`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        toast({
          title: "Thành công",
          description: "Đặt học kỳ hiện tại thành công"
        })
        if (selectedAcademicYear) {
          fetchSemestersByAcademicYear(selectedAcademicYear)
        } else {
          fetchSemesters()
        }
      }
    } catch (error) {
      console.error('Error setting current semester:', error)
      toast({
        title: "Lỗi",
        description: "Không thể đặt học kỳ hiện tại",
        variant: "destructive"
      })
    }
  }

  const resetForm = () => {
    setFormData({
      idNienKhoa: 0,
      tenHocKy: "",
      soTuan: 16,
      ngayBatDau: "",
      ngayKetThuc: "",
      hienTai: false,
      moTa: "",
      trangThai: true
    })
    setEditingSemester(null)
  }

  const handleDialogClose = () => {
    setDialogOpen(false)
    resetForm()
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'CHUA_BAT_DAU':
        return <Badge variant="secondary">Chưa bắt đầu</Badge>
      case 'DANG_DIEN_RA':
        return <Badge variant="default">Đang diễn ra</Badge>
      case 'DA_KET_THUC':
        return <Badge variant="outline">Đã kết thúc</Badge>
      default:
        return <Badge variant="secondary">Không xác định</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quản Lý Học Kỳ</h1>
          <p className="text-muted-foreground">
            Tạo và quản lý các học kỳ trong năm học
          </p>
        </div>
        <div className="flex space-x-2">
          <Select value={selectedAcademicYear?.toString() || ""} onValueChange={(value) => setSelectedAcademicYear(value ? parseInt(value) : null)}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Chọn năm học" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Tất cả năm học</SelectItem>
              {academicYears.map((year) => (
                <SelectItem key={year.idNienKhoa} value={year.idNienKhoa.toString()}>
                  {year.tenNienKhoa}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => setDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Thêm Học Kỳ
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {editingSemester ? 'Cập Nhật Học Kỳ' : 'Thêm Học Kỳ Mới'}
                </DialogTitle>
                <DialogDescription>
                  {editingSemester 
                    ? 'Cập nhật thông tin học kỳ' 
                    : 'Tạo một học kỳ mới trong năm học'
                  }
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit}>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="idNienKhoa" className="text-right">
                      Năm Học
                    </Label>
                    <Select 
                      value={formData.idNienKhoa.toString()} 
                      onValueChange={(value) => setFormData({...formData, idNienKhoa: parseInt(value)})}
                    >
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Chọn năm học" />
                      </SelectTrigger>
                      <SelectContent>
                        {academicYears.map((year) => (
                          <SelectItem key={year.idNienKhoa} value={year.idNienKhoa.toString()}>
                            {year.tenNienKhoa}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="tenHocKy" className="text-right">
                      Tên Học Kỳ
                    </Label>
                    <Input
                      id="tenHocKy"
                      value={formData.tenHocKy}
                      onChange={(e) => setFormData({...formData, tenHocKy: e.target.value})}
                      className="col-span-3"
                      placeholder="VD: Học kỳ 1"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="soTuan" className="text-right">
                      Số Tuần
                    </Label>
                    <Input
                      id="soTuan"
                      type="number"
                      value={formData.soTuan}
                      onChange={(e) => setFormData({...formData, soTuan: parseInt(e.target.value)})}
                      className="col-span-3"
                      min="1"
                      max="52"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="ngayBatDau" className="text-right">
                      Ngày Bắt Đầu
                    </Label>
                    <Input
                      id="ngayBatDau"
                      type="date"
                      value={formData.ngayBatDau}
                      onChange={(e) => setFormData({...formData, ngayBatDau: e.target.value})}
                      className="col-span-3"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="ngayKetThuc" className="text-right">
                      Ngày Kết Thúc
                    </Label>
                    <Input
                      id="ngayKetThuc"
                      type="date"
                      value={formData.ngayKetThuc}
                      onChange={(e) => setFormData({...formData, ngayKetThuc: e.target.value})}
                      className="col-span-3"
                      required
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={handleDialogClose}>
                    Hủy
                  </Button>
                  <Button type="submit">
                    {editingSemester ? 'Cập Nhật' : 'Tạo Mới'}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Danh Sách Học Kỳ</CardTitle>
          <CardDescription>
            Quản lý tất cả các học kỳ trong hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tên Học Kỳ</TableHead>
                <TableHead>Năm Học</TableHead>
                <TableHead>Thời Gian</TableHead>
                <TableHead>Số Tuần</TableHead>
                <TableHead>Trạng Thái</TableHead>
                <TableHead>Lịch Giảng</TableHead>
                <TableHead className="text-right">Thao Tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {semesters.map((semester) => (
                <TableRow key={semester.idHocKy}>
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-2">
                      {semester.hienTai && <Star className="h-4 w-4 text-yellow-500" />}
                      <span>{semester.tenHocKy}</span>
                    </div>
                  </TableCell>
                  <TableCell>{semester.tenNienKhoa}</TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>{new Date(semester.ngayBatDau).toLocaleDateString('vi-VN')}</div>
                      <div className="text-muted-foreground">
                        đến {new Date(semester.ngayKetThuc).toLocaleDateString('vi-VN')}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{semester.soTuan}</TableCell>
                  <TableCell>
                    {getStatusBadge(semester.trangThaiHocKy)}
                  </TableCell>
                  <TableCell>{semester.soLichGiang}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetCurrent(semester.idHocKy)}
                        disabled={semester.hienTai}
                      >
                        <Star className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(semester)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(semester.idHocKy)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
