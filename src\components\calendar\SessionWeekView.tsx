'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Plus, Sun, Sunset, Moon } from 'lucide-react'

interface Schedule {
  id: number
  subject: string
  class: string
  teacher: string
  room: string
  startTime: string
  endTime: string
  date: string
  status: 'confirmed' | 'pending' | 'cancelled'
  color: string
}

interface SessionWeekViewProps {
  currentDate: Date
  schedules: Schedule[]
  onScheduleClick?: (schedule: Schedule) => void
  onDateClick?: (date: string, timeInfo?: { session?: string; timeSlot?: string }) => void
  onCreateSchedule?: (date: string, timeInfo?: { session?: string; timeSlot?: string }) => void
  isAdmin?: boolean
}

type Session = 'morning' | 'afternoon' | 'evening'

export default function SessionWeekView({
  currentDate,
  schedules,
  onScheduleClick,
  onDateClick,
  onCreateSchedule,
  isAdmin = false
}: SessionWeekViewProps) {
  const getDaysInWeek = (date: Date) => {
    const startOfWeek = new Date(date)
    const day = startOfWeek.getDay()
    const diff = startOfWeek.getDate() - day
    startOfWeek.setDate(diff)

    const days = []
    for (let i = 0; i < 7; i++) {
      const currentDay = new Date(startOfWeek)
      currentDay.setDate(startOfWeek.getDate() + i)
      days.push(currentDay)
    }

    return days
  }

  const getSchedulesForDate = (date: Date) => {
    const dateString = date.toISOString().split('T')[0]
    return schedules.filter(schedule => schedule.date === dateString)
  }

  const getSchedulesForSession = (date: Date, session: Session) => {
    const daySchedules = getSchedulesForDate(date)
    return daySchedules.filter(schedule => {
      const hour = parseInt(schedule.startTime.split(':')[0])
      switch (session) {
        case 'morning':
          return hour >= 6 && hour < 12
        case 'afternoon':
          return hour >= 12 && hour < 18
        case 'evening':
          return hour >= 18 && hour <= 23
        default:
          return false
      }
    })
  }

  const sessions: { key: Session; name: string; icon: any; timeRange: string; bgColor: string }[] = [
    {
      key: 'morning',
      name: 'Buổi sáng',
      icon: Sun,
      timeRange: '06:00 - 12:00',
      bgColor: 'bg-yellow-50'
    },
    {
      key: 'afternoon',
      name: 'Buổi chiều',
      icon: Sunset,
      timeRange: '12:00 - 18:00',
      bgColor: 'bg-orange-50'
    },
    {
      key: 'evening',
      name: 'Buổi tối',
      icon: Moon,
      timeRange: '18:00 - 23:00',
      bgColor: 'bg-blue-50'
    }
  ]

  const dayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7']
  const weekDays = getDaysInWeek(currentDate)

  const isToday = (date: Date) => {
    return date.toDateString() === new Date().toDateString()
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <Badge variant="default" className="text-xs">Đã xác nhận</Badge>
      case 'pending':
        return <Badge variant="secondary" className="text-xs">Chờ duyệt</Badge>
      case 'cancelled':
        return <Badge variant="destructive" className="text-xs">Đã hủy</Badge>
      default:
        return null
    }
  }

  const getDefaultTimeForSession = (session: Session) => {
    switch (session) {
      case 'morning':
        return '07:00'
      case 'afternoon':
        return '13:00'
      case 'evening':
        return '19:00'
      default:
        return '07:00'
    }
  }

  return (
    <div className="space-y-6">
      {sessions.map((session) => (
        <Card key={session.key} className={session.bgColor}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-lg">
              <session.icon className="h-5 w-5 mr-2" />
              {session.name}
              <span className="ml-2 text-sm font-normal text-gray-500">
                ({session.timeRange})
              </span>
            </CardTitle>
          </CardHeader>

          <CardContent>
            <div className="grid grid-cols-7 gap-2">
              {weekDays.map((date, dayIndex) => {
                const sessionSchedules = getSchedulesForSession(date, session.key)
                const todayClass = isToday(date) ? 'ring-2 ring-blue-500 bg-blue-50' : ''

                return (
                  <div
                    key={`${session.key}-${dayIndex}`}
                    className={`min-h-[120px] p-3 border border-gray-200 rounded-lg bg-white hover:shadow-md transition-shadow cursor-pointer ${todayClass}`}
                    onClick={() => {
                      const timeInfo = {
                        session: session.key,
                        timeSlot: getDefaultTimeForSession(session.key)
                      }
                      onDateClick?.(date.toISOString().split('T')[0], timeInfo)
                    }}
                  >
                    {/* Day Header */}
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-center">
                        <div className="text-xs text-gray-500 font-medium">
                          {dayNames[date.getDay()]}
                        </div>
                        <div className={`text-sm font-bold ${
                          isToday(date) ? 'text-blue-600' : 'text-gray-900'
                        }`}>
                          {date.getDate()}/{date.getMonth() + 1}
                        </div>
                      </div>

                      {isAdmin && (
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                          onClick={(e) => {
                            e.stopPropagation()
                            const timeInfo = {
                              session: session.key,
                              timeSlot: getDefaultTimeForSession(session.key)
                            }
                            onCreateSchedule?.(date.toISOString().split('T')[0], timeInfo)
                          }}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      )}
                    </div>

                    {/* Schedules */}
                    <div className="space-y-1">
                      {sessionSchedules.length > 0 ? (
                        sessionSchedules.map((schedule) => (
                          <div
                            key={schedule.id}
                            className={`${schedule.color} text-white p-2 rounded text-xs cursor-pointer hover:opacity-80 transition-opacity`}
                            onClick={(e) => {
                              e.stopPropagation()
                              onScheduleClick?.(schedule)
                            }}
                          >
                            <div className="font-medium truncate">{schedule.subject}</div>
                            <div className="truncate">{schedule.class}</div>
                            <div className="truncate">{schedule.room}</div>
                            <div className="flex items-center justify-between mt-1">
                              <span className="text-xs">
                                {schedule.startTime} - {schedule.endTime}
                              </span>
                              {getStatusBadge(schedule.status)}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center text-gray-400 text-xs py-4">
                          Không có lịch
                        </div>
                      )}
                    </div>

                    {/* Schedule Count */}
                    {sessionSchedules.length > 0 && (
                      <div className="mt-2 text-center">
                        <span className="text-xs text-gray-500">
                          {sessionSchedules.length} lịch giảng
                        </span>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
