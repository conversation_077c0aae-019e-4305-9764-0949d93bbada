import { API_CONFIG, createApiUrl, createRequestConfig, ApiResponse, ApiError } from '@/lib/config/api'

// Custom error class for API errors
export class ApiClientError extends Error {
  public status: number
  public response?: any

  constructor(message: string, status: number, response?: any) {
    super(message)
    this.name = 'ApiClientError'
    this.status = status
    this.response = response
  }
}

// Main API client class
export class ApiClient {
  private baseUrl: string
  private timeout: number

  constructor() {
    this.baseUrl = API_CONFIG.BASE_URL
    this.timeout = API_CONFIG.TIMEOUT
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    options: {
      method?: string
      headers?: Record<string, string>
      body?: any
      timeout?: number
    } = {}
  ): Promise<T> {
    const url = createApiUrl(endpoint)
    const config = createRequestConfig(options)

    // Add timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), options.timeout || this.timeout)
    config.signal = controller.signal

    try {
      const response = await fetch(url, config)
      clearTimeout(timeoutId)

      // Parse response
      let data: any
      const contentType = response.headers.get('content-type')

      if (contentType && contentType.includes('application/json')) {
        data = await response.json()
      } else {
        data = await response.text()
      }

      // Handle HTTP errors
      if (!response.ok) {
        throw new ApiClientError(
          data?.message || `HTTP Error: ${response.status}`,
          response.status,
          data
        )
      }

      return data
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof ApiClientError) {
        throw error
      }

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new ApiClientError('Request timeout', 408)
        }
        throw new ApiClientError(error.message, 0)
      }

      throw new ApiClientError('Unknown error occurred', 0)
    }
  }

  // GET request
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    let url = endpoint
    if (params) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      url += `?${searchParams.toString()}`
    }

    return this.request<T>(url, { method: 'GET' })
  }

  // POST request
  async post<T>(endpoint: string, body?: any, options?: { params?: Record<string, any>, headers?: Record<string, string> }): Promise<T> {
    let url = endpoint
    if (options?.params) {
      const searchParams = new URLSearchParams()
      Object.entries(options.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      url += `?${searchParams.toString()}`
    }

    return this.request<T>(url, {
      method: 'POST',
      body,
      headers: options?.headers
    })
  }

  // PUT request
  async put<T>(endpoint: string, body?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body
    })
  }

  // PATCH request
  async patch<T>(endpoint: string, body?: any, options?: { params?: Record<string, any>, headers?: Record<string, string> }): Promise<T> {
    let url = endpoint
    if (options?.params) {
      const searchParams = new URLSearchParams()
      Object.entries(options.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      url += `?${searchParams.toString()}`
    }

    return this.request<T>(url, {
      method: 'PATCH',
      body,
      headers: options?.headers
    })
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  // Upload file
  async upload<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData
    })
  }

  // Download file
  async download(endpoint: string, filename?: string): Promise<void> {
    const url = createApiUrl(endpoint)
    const config = createRequestConfig({ method: 'GET' })

    try {
      const response = await fetch(url, config)

      if (!response.ok) {
        throw new ApiClientError(`Download failed: ${response.status}`, response.status)
      }

      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      window.URL.revokeObjectURL(downloadUrl)
    } catch (error) {
      if (error instanceof ApiClientError) {
        throw error
      }
      throw new ApiClientError('Download failed', 0)
    }
  }
}

// Create singleton instance
export const apiClient = new ApiClient()

// Convenience functions using the singleton
export const api = {
  get: <T>(endpoint: string, params?: Record<string, any>) =>
    apiClient.get<T>(endpoint, params),

  post: <T>(endpoint: string, body?: any, options?: { params?: Record<string, any>, headers?: Record<string, string> }) =>
    apiClient.post<T>(endpoint, body, options),

  put: <T>(endpoint: string, body?: any) =>
    apiClient.put<T>(endpoint, body),

  patch: <T>(endpoint: string, body?: any, options?: { params?: Record<string, any>, headers?: Record<string, string> }) =>
    apiClient.patch<T>(endpoint, body, options),

  delete: <T>(endpoint: string) =>
    apiClient.delete<T>(endpoint),

  upload: <T>(endpoint: string, file: File, additionalData?: Record<string, any>) =>
    apiClient.upload<T>(endpoint, file, additionalData),

  download: (endpoint: string, filename?: string) =>
    apiClient.download(endpoint, filename)
}

export default apiClient
