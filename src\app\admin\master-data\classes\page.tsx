"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Plus, Edit, Trash2, GraduationCap, Search, Users } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { masterDataService, ClassInfo, Department } from "@/lib/services/masterDataService"

export default function ClassesPage() {
  const [classes, setClasses] = useState<ClassInfo[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingClass, setEditingClass] = useState<ClassInfo | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [formData, setFormData] = useState({
    maLop: "",
    tenLop: "",
    siSo: 30,
    khoaHoc: "",
    idKhoa: 0,
    trangThai: true
  })

  useEffect(() => {
    fetchClasses()
    fetchDepartments()
  }, [])

  const fetchClasses = async () => {
    try {
      setLoading(true)
      const response = await masterDataService.getClasses(0, 100, searchTerm)
      setClasses(response.content || [])
    } catch (error) {
      console.error('Error fetching classes:', error)
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách lớp học",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchDepartments = async () => {
    try {
      const depts = await masterDataService.getAllDepartments()
      setDepartments(depts)
    } catch (error) {
      console.error('Error fetching departments:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (formData.idKhoa === 0) {
      toast({
        title: "Lỗi",
        description: "Vui lòng chọn khoa",
        variant: "destructive"
      })
      return
    }

    try {
      if (editingClass) {
        await masterDataService.updateClass(editingClass.id!, formData)
        toast({
          title: "Thành công",
          description: "Cập nhật lớp học thành công"
        })
      } else {
        await masterDataService.createClass(formData)
        toast({
          title: "Thành công",
          description: "Tạo lớp học mới thành công"
        })
      }
      setDialogOpen(false)
      resetForm()
      fetchClasses()
    } catch (error) {
      console.error('Error saving class:', error)
      toast({
        title: "Lỗi",
        description: "Không thể lưu thông tin lớp học",
        variant: "destructive"
      })
    }
  }

  const handleEdit = (classInfo: ClassInfo) => {
    setEditingClass(classInfo)
    setFormData({
      maLop: classInfo.maLop,
      tenLop: classInfo.tenLop,
      siSo: classInfo.siSo,
      khoaHoc: classInfo.khoaHoc,
      idKhoa: classInfo.idKhoa,
      trangThai: classInfo.trangThai
    })
    setDialogOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (!confirm('Bạn có chắc chắn muốn xóa lớp học này?')) {
      return
    }

    try {
      await masterDataService.deleteClass(id)
      toast({
        title: "Thành công",
        description: "Xóa lớp học thành công"
      })
      fetchClasses()
    } catch (error) {
      console.error('Error deleting class:', error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa lớp học",
        variant: "destructive"
      })
    }
  }

  const resetForm = () => {
    setFormData({
      maLop: "",
      tenLop: "",
      siSo: 30,
      khoaHoc: "",
      idKhoa: 0,
      trangThai: true
    })
    setEditingClass(null)
  }

  const handleSearch = () => {
    fetchClasses()
  }

  const getDepartmentName = (idKhoa: number) => {
    const dept = departments.find(d => d.id === idKhoa)
    return dept?.tenKhoa || 'N/A'
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Quản Lý Lớp Học</h1>
          <p className="text-muted-foreground">
            Quản lý thông tin lớp học và sinh viên
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="mr-2 h-4 w-4" />
              Thêm Lớp Học
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {editingClass ? "Cập nhật lớp học" : "Thêm lớp học mới"}
              </DialogTitle>
              <DialogDescription>
                Nhập thông tin lớp học. Nhấn lưu khi hoàn tất.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="maLop" className="text-right">
                    Mã lớp *
                  </Label>
                  <Input
                    id="maLop"
                    value={formData.maLop}
                    onChange={(e) => setFormData({...formData, maLop: e.target.value})}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="tenLop" className="text-right">
                    Tên lớp *
                  </Label>
                  <Input
                    id="tenLop"
                    value={formData.tenLop}
                    onChange={(e) => setFormData({...formData, tenLop: e.target.value})}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="idKhoa" className="text-right">
                    Khoa *
                  </Label>
                  <Select
                    value={formData.idKhoa.toString()}
                    onValueChange={(value) => setFormData({...formData, idKhoa: parseInt(value)})}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Chọn khoa" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id!.toString()}>
                          {dept.tenKhoa}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="khoaHoc" className="text-right">
                    Khóa học *
                  </Label>
                  <Input
                    id="khoaHoc"
                    value={formData.khoaHoc}
                    onChange={(e) => setFormData({...formData, khoaHoc: e.target.value})}
                    className="col-span-3"
                    placeholder="VD: 2020-2024"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="siSo" className="text-right">
                    Sĩ số
                  </Label>
                  <Input
                    id="siSo"
                    type="number"
                    min="1"
                    max="100"
                    value={formData.siSo}
                    onChange={(e) => setFormData({...formData, siSo: parseInt(e.target.value)})}
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">Lưu</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Tìm kiếm</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="Tìm kiếm theo tên hoặc mã lớp..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <Button onClick={handleSearch}>
              <Search className="mr-2 h-4 w-4" />
              Tìm kiếm
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Classes Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <GraduationCap className="mr-2 h-5 w-5" />
            Danh sách lớp học ({classes.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">Đang tải...</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Mã lớp</TableHead>
                  <TableHead>Tên lớp</TableHead>
                  <TableHead>Khoa</TableHead>
                  <TableHead>Khóa học</TableHead>
                  <TableHead>Sĩ số</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {classes.map((classInfo) => (
                  <TableRow key={classInfo.id}>
                    <TableCell className="font-medium">{classInfo.maLop}</TableCell>
                    <TableCell>{classInfo.tenLop}</TableCell>
                    <TableCell>{getDepartmentName(classInfo.idKhoa)}</TableCell>
                    <TableCell>{classInfo.khoaHoc}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Users className="mr-1 h-4 w-4" />
                        {classInfo.siSo}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={classInfo.trangThai ? "default" : "secondary"}>
                        {classInfo.trangThai ? "Hoạt động" : "Không hoạt động"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(classInfo)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(classInfo.id!)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
