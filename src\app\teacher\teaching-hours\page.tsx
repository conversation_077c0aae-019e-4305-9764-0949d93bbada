"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Clock, 
  ArrowLeft,
  Calendar,
  BarChart3,
  Download,
  Filter
} from "lucide-react"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface TeachingHour {
  id: number
  subject: string
  class: string
  date: string
  startTime: string
  endTime: string
  hours: number
  status: 'completed' | 'scheduled' | 'cancelled'
}

export default function TeachingHours() {
  const [teachingHours, setTeachingHours] = useState<TeachingHour[]>([])
  const [loading, setLoading] = useState(true)
  const [totalHours, setTotalHours] = useState(0)
  const [thisMonthHours, setThisMonthHours] = useState(0)
  const router = useRouter()

  useEffect(() => {
    // <PERSON><PERSON><PERSON> tra quyền truy cập
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    // Mock data
    const mockData = [
      {
        id: 1,
        subject: "Lập trình Java",
        class: "CNTT01",
        date: "2024-01-15",
        startTime: "07:00",
        endTime: "09:00",
        hours: 2,
        status: "completed" as const
      },
      {
        id: 2,
        subject: "Cơ sở dữ liệu",
        class: "CNTT02",
        date: "2024-01-16",
        startTime: "09:00",
        endTime: "11:00",
        hours: 2,
        status: "completed" as const
      },
      {
        id: 3,
        subject: "Mạng máy tính",
        class: "CNTT01",
        date: "2024-01-18",
        startTime: "13:00",
        endTime: "15:00",
        hours: 2,
        status: "scheduled" as const
      }
    ]

    setTeachingHours(mockData)
    setTotalHours(mockData.reduce((sum, item) => sum + item.hours, 0))
    setThisMonthHours(mockData.filter(item => item.status === 'completed').reduce((sum, item) => sum + item.hours, 0))
    setLoading(false)
  }, [router])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-500">Đã hoàn thành</Badge>
      case 'scheduled':
        return <Badge variant="secondary">Đã lên lịch</Badge>
      case 'cancelled':
        return <Badge variant="destructive">Đã hủy</Badge>
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/teacher')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Giờ Giảng Dạy</h1>
            <p className="text-muted-foreground">
              Theo dõi và quản lý giờ giảng dạy của bạn
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Bộ lọc
          </Button>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            Xuất báo cáo
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-3 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng giờ giảng</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalHours}</div>
            <p className="text-xs text-muted-foreground">
              Tất cả thời gian
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tháng này</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{thisMonthHours}</div>
            <p className="text-xs text-muted-foreground">
              Giờ đã hoàn thành
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Trung bình/tuần</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(thisMonthHours / 4)}</div>
            <p className="text-xs text-muted-foreground">
              Giờ mỗi tuần
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Teaching Hours Table */}
      <Card>
        <CardHeader>
          <CardTitle>Chi tiết giờ giảng</CardTitle>
          <CardDescription>
            Danh sách tất cả các buổi giảng dạy của bạn
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Môn học</TableHead>
                <TableHead>Lớp</TableHead>
                <TableHead>Ngày</TableHead>
                <TableHead>Thời gian</TableHead>
                <TableHead>Số giờ</TableHead>
                <TableHead>Trạng thái</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {teachingHours.map((hour) => (
                <TableRow key={hour.id}>
                  <TableCell className="font-medium">{hour.subject}</TableCell>
                  <TableCell>{hour.class}</TableCell>
                  <TableCell>{new Date(hour.date).toLocaleDateString('vi-VN')}</TableCell>
                  <TableCell>{hour.startTime} - {hour.endTime}</TableCell>
                  <TableCell>{hour.hours} giờ</TableCell>
                  <TableCell>{getStatusBadge(hour.status)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </main>
  )
}
