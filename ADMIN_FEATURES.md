# 🔧 Chức Năng Quản Trị Hệ Thống

## 📋 Tổng Quan

Tài liệu này mô tả các chức năng quản trị mới được thêm vào hệ thống quản lý lị<PERSON> g<PERSON>, bao gồm quản lý thông tin chung và dữ liệu danh mục cần thiết cho việc sắp xếp lịch giảng.

## 🏗️ Kiến Trúc Chức Năng

### 1. **AdminController** - Quản Trị Tổng Quan
- **Endpoint**: `/api/admin`
- **Chức năng**:
  - Dashboard quản trị
  - Thông tin hệ thống
  - Khởi tạo dữ liệu
  - Sao lưu dữ liệu
  - Thống kê hệ thống

### 2. **AcademicYearController** - Quản L<PERSON>
- **Endpoint**: `/api/academic-years`
- **Ch<PERSON><PERSON> năng**:
  - CRUD năm học/niên khóa
  - <PERSON><PERSON><PERSON> hoạt năm học hiện tại
  - Thống kê theo năm học

### 3. **SemesterController** - Quản Lý Học Kỳ
- **Endpoint**: `/api/semesters`
- **Chức năng**:
  - CRUD học kỳ
  - Đặt học kỳ hiện tại
  - Thống kê theo học kỳ
  - Tự động cập nhật trạng thái

### 4. **MasterDataController** - Quản Lý Danh Mục
- **Endpoint**: `/api/master-data`
- **Chức năng**:
  - Quản lý khoa/phòng ban
  - Quản lý môn học
  - Quản lý lớp học
  - Quản lý phòng học
  - Quản lý cơ sở
  - Quản lý giảng viên
  - Import/Export dữ liệu

## 🔐 Phân Quyền

### ADMIN (Toàn quyền)
- ✅ Tất cả chức năng quản trị
- ✅ Tạo/sửa/xóa tất cả dữ liệu
- ✅ Quản lý năm học và học kỳ
- ✅ Import/Export dữ liệu
- ✅ Sao lưu và khôi phục

### TRUONG_KHOA (Quản lý khoa)
- ✅ Xem thông tin hệ thống
- ✅ Xem danh sách năm học/học kỳ
- ✅ Xem dữ liệu danh mục
- ❌ Không thể tạo/sửa/xóa

### GIANG_VIEN (Chỉ xem)
- ✅ Xem năm học/học kỳ hiện tại
- ❌ Không truy cập chức năng quản trị

## 📊 API Endpoints

### Admin Management
```http
GET    /api/admin/dashboard           # Dashboard quản trị
GET    /api/admin/system-info         # Thông tin hệ thống
POST   /api/admin/initialize-data     # Khởi tạo dữ liệu
POST   /api/admin/backup              # Sao lưu dữ liệu
GET    /api/admin/statistics          # Thống kê hệ thống
```

### Academic Year Management
```http
GET    /api/academic-years            # Danh sách năm học
GET    /api/academic-years/active     # Năm học đang hoạt động
GET    /api/academic-years/{id}       # Chi tiết năm học
POST   /api/academic-years            # Tạo năm học mới
PUT    /api/academic-years/{id}       # Cập nhật năm học
DELETE /api/academic-years/{id}       # Xóa năm học
POST   /api/academic-years/{id}/activate # Kích hoạt năm học
```

### Semester Management
```http
GET    /api/semesters                 # Danh sách học kỳ
GET    /api/semesters/current         # Học kỳ hiện tại
GET    /api/semesters/academic-year/{id} # Học kỳ theo năm học
GET    /api/semesters/{id}            # Chi tiết học kỳ
POST   /api/semesters                 # Tạo học kỳ mới
PUT    /api/semesters/{id}            # Cập nhật học kỳ
DELETE /api/semesters/{id}            # Xóa học kỳ
POST   /api/semesters/{id}/set-current # Đặt học kỳ hiện tại
GET    /api/semesters/{id}/statistics # Thống kê học kỳ
```

### Master Data Management
```http
# Khoa/Phòng ban
GET    /api/master-data/departments   # Danh sách khoa
POST   /api/master-data/departments   # Tạo khoa mới

# Môn học
GET    /api/master-data/subjects      # Danh sách môn học
POST   /api/master-data/subjects      # Tạo môn học mới

# Lớp học
GET    /api/master-data/classes       # Danh sách lớp học
POST   /api/master-data/classes       # Tạo lớp học mới

# Phòng học
GET    /api/master-data/rooms         # Danh sách phòng học
POST   /api/master-data/rooms         # Tạo phòng học mới

# Cơ sở
GET    /api/master-data/campuses      # Danh sách cơ sở
POST   /api/master-data/campuses      # Tạo cơ sở mới

# Giảng viên
GET    /api/master-data/teachers      # Danh sách giảng viên
POST   /api/master-data/teachers      # Tạo giảng viên mới

# Import/Export
POST   /api/master-data/import        # Import dữ liệu từ Excel
```

## 📝 Ví Dụ Sử Dụng

### 1. Tạo Năm Học Mới
```json
POST /api/academic-years
{
  "tenNienKhoa": "2024-2025",
  "nam": 2024,
  "moTa": "Năm học 2024-2025",
  "trangThai": true
}
```

### 2. Tạo Học Kỳ Mới
```json
POST /api/semesters
{
  "idNienKhoa": 1,
  "tenHocKy": "Học kỳ 1",
  "soTuan": 16,
  "ngayBatDau": "2024-09-01",
  "ngayKetThuc": "2024-12-31",
  "hienTai": true
}
```

### 3. Tạo Khoa Mới
```json
POST /api/master-data/departments
{
  "maKhoa": "CNTT",
  "tenKhoa": "Công nghệ thông tin",
  "moTa": "Khoa Công nghệ thông tin",
  "email": "<EMAIL>",
  "trangThai": true
}
```

## 🔄 Quy Trình Quản Trị

### 1. Thiết Lập Ban Đầu
1. Tạo năm học mới
2. Tạo các học kỳ trong năm
3. Thiết lập các danh mục cơ bản (khoa, cơ sở, phòng học)
4. Import dữ liệu từ hệ thống cũ (nếu có)

### 2. Quản Lý Hàng Ngày
1. Theo dõi dashboard
2. Kiểm tra thống kê hệ thống
3. Quản lý dữ liệu danh mục
4. Sao lưu dữ liệu định kỳ

### 3. Chuyển Đổi Học Kỳ
1. Tạo học kỳ mới
2. Đặt học kỳ hiện tại
3. Cập nhật trạng thái các học kỳ cũ
4. Thống kê và báo cáo

## 🚀 Tính Năng Nâng Cao

### 1. Import/Export Dữ Liệu
- Hỗ trợ import từ file Excel
- Export template chuẩn
- Validation dữ liệu tự động
- Báo cáo lỗi chi tiết

### 2. Đồng Bộ Dữ Liệu
- Kết nối với hệ thống quản lý sinh viên
- Đồng bộ thông tin giảng viên
- Cập nhật danh sách lớp học tự động

### 3. Thống Kê và Báo Cáo
- Dashboard real-time
- Báo cáo theo thời gian
- Thống kê sử dụng hệ thống
- Export báo cáo Excel/PDF

## 🔧 Cấu Hình

### Database
Các bảng mới được thêm vào:
- Mở rộng bảng `NIEN_KHOA`
- Mở rộng bảng `HOC_KY`
- Thêm các trường quản lý trong các bảng danh mục

### Security
- JWT Authentication cho tất cả endpoints
- Role-based authorization
- Audit logging cho các thao tác quan trọng

### Performance
- Caching cho dữ liệu danh mục
- Pagination cho danh sách lớn
- Indexing cho các truy vấn thường xuyên
