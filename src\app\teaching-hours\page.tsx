'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Clock,
  ArrowLeft,
  Calendar,
  BookOpen,
  TrendingUp,
  Download
} from 'lucide-react'
import { api } from '@/lib/utils/apiClient'
import { API_CONFIG } from '@/lib/config/api'

interface TeachingHour {
  id: number
  tenMonHoc: string
  tenLop: string
  soTiet: number
  heSo: number
  gioQuyDoi: number
  thang: number
  nam: number
}

export default function TeachingHoursPage() {
  const [teachingHours, setTeachingHours] = useState<TeachingHour[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [totalHours, setTotalHours] = useState(0)
  const router = useRouter()

  useEffect(() => {
    // Kiểm tra token
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    fetchTeachingHours()
  }, [router, selectedMonth, selectedYear])

  const fetchTeachingHours = async () => {
    try {
      const response = await api.get(API_CONFIG.ENDPOINTS.TEACHING_HOURS.BASE, {
        month: selectedMonth,
        year: selectedYear
      })

      if (response.success) {
        setTeachingHours(response.data)
        calculateTotalHours(response.data)
      }
    } catch (error) {
      console.error('Error fetching teaching hours:', error)
      // Mock data for demo
      const mockData = [
        {
          id: 1,
          tenMonHoc: 'Lập trình Java',
          tenLop: 'CNTT01',
          soTiet: 15,
          heSo: 1.0,
          gioQuyDoi: 15.0,
          thang: selectedMonth,
          nam: selectedYear
        },
        {
          id: 2,
          tenMonHoc: 'Cơ sở dữ liệu',
          tenLop: 'CNTT02',
          soTiet: 12,
          heSo: 1.2,
          gioQuyDoi: 14.4,
          thang: selectedMonth,
          nam: selectedYear
        },
        {
          id: 3,
          tenMonHoc: 'Mạng máy tính',
          tenLop: 'CNTT03',
          soTiet: 10,
          heSo: 1.0,
          gioQuyDoi: 10.0,
          thang: selectedMonth,
          nam: selectedYear
        }
      ]
      setTeachingHours(mockData)
      calculateTotalHours(mockData)
    } finally {
      setLoading(false)
    }
  }

  const calculateTotalHours = (hours: TeachingHour[]) => {
    const total = hours.reduce((sum, hour) => sum + hour.gioQuyDoi, 0)
    setTotalHours(total)
  }

  const handleExportExcel = async () => {
    try {
      await api.download(
        `/api/teaching-hours/export/excel?month=${selectedMonth}&year=${selectedYear}`,
        `gio-giang-${selectedMonth}-${selectedYear}.xlsx`
      )
    } catch (error) {
      console.error('Error exporting Excel:', error)
      alert('Có lỗi xảy ra khi xuất file Excel')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/dashboard')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại
              </Button>
              <Clock className="h-6 w-6 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">
                Quản lý giờ giảng dạy
              </h1>
            </div>
            <Button onClick={handleExportExcel}>
              <Download className="h-4 w-4 mr-2" />
              Xuất Excel
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Bộ lọc thời gian</CardTitle>
            <CardDescription>
              Chọn tháng và năm để xem giờ giảng dạy
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="month">Tháng</Label>
                <Input
                  id="month"
                  type="number"
                  min="1"
                  max="12"
                  value={selectedMonth}
                  onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                />
              </div>
              <div>
                <Label htmlFor="year">Năm</Label>
                <Input
                  id="year"
                  type="number"
                  min="2020"
                  max="2030"
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                />
              </div>
              <div className="flex items-end">
                <Button onClick={fetchTeachingHours} className="w-full">
                  Tìm kiếm
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tổng giờ quy đổi</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalHours.toFixed(1)}</div>
              <p className="text-xs text-muted-foreground">
                Tháng {selectedMonth}/{selectedYear}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Số môn học</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{teachingHours.length}</div>
              <p className="text-xs text-muted-foreground">
                Môn học đã giảng
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Giờ trung bình</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {teachingHours.length > 0 ? (totalHours / teachingHours.length).toFixed(1) : '0'}
              </div>
              <p className="text-xs text-muted-foreground">
                Giờ/môn học
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Teaching Hours Table */}
        <Card>
          <CardHeader>
            <CardTitle>Chi tiết giờ giảng dạy</CardTitle>
            <CardDescription>
              Danh sách chi tiết các môn học và giờ giảng trong tháng {selectedMonth}/{selectedYear}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {teachingHours.length === 0 ? (
              <div className="text-center py-12">
                <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Không có dữ liệu giờ giảng
                </h3>
                <p className="text-gray-500">
                  Không tìm thấy giờ giảng nào trong tháng {selectedMonth}/{selectedYear}.
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-4 font-medium">Môn học</th>
                      <th className="text-left p-4 font-medium">Lớp</th>
                      <th className="text-right p-4 font-medium">Số tiết</th>
                      <th className="text-right p-4 font-medium">Hệ số</th>
                      <th className="text-right p-4 font-medium">Giờ quy đổi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {teachingHours.map((hour) => (
                      <tr key={hour.id} className="border-b hover:bg-gray-50">
                        <td className="p-4 font-medium">{hour.tenMonHoc}</td>
                        <td className="p-4">{hour.tenLop}</td>
                        <td className="p-4 text-right">{hour.soTiet}</td>
                        <td className="p-4 text-right">{hour.heSo}</td>
                        <td className="p-4 text-right font-medium">{hour.gioQuyDoi.toFixed(1)}</td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="border-t-2 font-bold">
                      <td className="p-4" colSpan={4}>Tổng cộng</td>
                      <td className="p-4 text-right">{totalHours.toFixed(1)}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
