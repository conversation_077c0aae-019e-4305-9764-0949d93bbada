import { api } from '@/lib/utils/apiClient'
import { API_CONFIG } from '@/lib/config/api'
import { masterDataService, Subject, ClassInfo, Room, Teacher } from './masterDataService'

// Calendar Event interface from API response
export interface CalendarEvent {
  id: number
  title: string
  description: string
  start: string // ISO datetime
  end: string // ISO datetime
  allDay: boolean
  backgroundColor: string
  borderColor: string
  textColor: string
  extendedProps: {
    scheduleId: number
    teacherId: number
    teacherName: string
    subjectCode: string
    subjectName: string
    classCode: string
    className: string
    roomName: string
    campusName: string
    sessionName: string
    formType: string
    practiceGroup?: string
    credits: number
    coefficient: number
    note?: string
  }
}

// Schedule interface for frontend display (converted from CalendarEvent)
export interface Schedule {
  id: number
  subject: string
  class: string
  teacher: string
  room: string
  dayOfWeek: string
  startTime: string
  endTime: string
  scheduleDate: string
  status: 'active' | 'pending' | 'cancelled'
  notes?: string
  periods?: number
  lessonType?: 'LT' | 'TH'
  color?: string
  startDate?: string
  endDate?: string
  date?: string // For calendar compatibility
  // Additional fields from API
  subjectCode?: string
  classCode?: string
  campusName?: string
  sessionName?: string
  practiceGroup?: string
  credits?: number
  coefficient?: number
}

// API Response interface
export interface ScheduleCalendarResponse {
  success: boolean
  message: string
  data: CalendarEvent[]
}

// Types for frontend form
export interface ScheduleFormData {
  // Ngành và lớp
  major: string
  class: string
  studyType: 'LT' | 'TH'
  group?: string

  // Môn học và bài học
  subject: string
  lesson: string
  lessonType: 'LT' | 'TH'
  periods: number
  coefficient: number

  // Giảng viên
  department: string
  teacher: string

  // Thời gian
  dayOfWeek: string
  session: 'morning' | 'afternoon' | 'evening'
  scheduleDate: string

  // Địa điểm
  campus: 'CS1' | 'CS2'
  areaType: 'LT' | 'TH'
  room: string

  // Thông tin bổ sung
  notes?: string
  academicYear?: string
  semester?: string
}

// Backend API request format (based on error message)
export interface BackendScheduleRequest {
  idBuoi: number        // Session ID
  soTiet: number        // Number of periods
  idPhong: number       // Room ID
  idMonHoc: number      // Subject ID
  idHocKy: number       // Semester ID
  idCanBo: number       // Teacher ID
  idLop: number         // Class ID
  idHinhThuc: number    // Study type ID
  thuHoc: number        // Day of week (1-7)
  ngayHoc?: string      // Schedule date
  ghiChu?: string       // Notes
}

// Backend API response types (simplified)
export interface Schedule {
  id: number
  subject: string
  className: string
  teacher: string
  room: string
  dayOfWeek: string
  startTime: string
  endTime: string
  scheduleDate: string
  status: string
  notes?: string
  // Add other fields as needed based on actual API response
}

// Helper functions to get IDs from master data
const getMasterDataIds = {
  // Get session ID from session name
  getSessionId: (session: string): number => {
    const sessionMap: Record<string, number> = {
      'morning': 1,    // Buổi sáng
      'afternoon': 2,  // Buổi chiều
      'evening': 3     // Buổi tối
    }
    return sessionMap[session] || 1
  },

  // Get day of week number (1=Monday, 7=Sunday)
  getDayOfWeekNumber: (dayOfWeek: string): number => {
    const dayMap: Record<string, number> = {
      'monday': 2,    // Thứ 2
      'tuesday': 3,   // Thứ 3
      'wednesday': 4, // Thứ 4
      'thursday': 5,  // Thứ 5
      'friday': 6,    // Thứ 6
      'saturday': 7,  // Thứ 7
      'sunday': 1     // Chủ nhật
    }
    return dayMap[dayOfWeek] || 2
  },

  // Get study type ID
  getStudyTypeId: (studyType: string): number => {
    return studyType === 'TH' ? 2 : 1 // 1=LT, 2=TH
  }
}

// Convert frontend form data to backend request format
const convertToBackendRequest = async (formData: ScheduleFormData): Promise<BackendScheduleRequest> => {
  try {
    // Get IDs from master data APIs
    const [subjectId, classId, roomId, teacherId] = await Promise.all([
      findSubjectId(formData.subject),
      findClassId(formData.class),
      findRoomId(formData.room),
      findTeacherId(formData.teacher)
    ])

    return {
      idBuoi: getMasterDataIds.getSessionId(formData.session),
      soTiet: formData.periods,
      idPhong: roomId,
      idMonHoc: subjectId,
      idHocKy: 1, // Default semester for now
      idCanBo: teacherId,
      idLop: classId,
      idHinhThuc: getMasterDataIds.getStudyTypeId(formData.studyType),
      thuHoc: getMasterDataIds.getDayOfWeekNumber(formData.dayOfWeek),
      ngayHoc: formData.scheduleDate,
      ghiChu: formData.notes
    }
  } catch (error) {
    console.error('Error converting form data to backend request:', error)
    // Fallback to default IDs if master data lookup fails
    return {
      idBuoi: getMasterDataIds.getSessionId(formData.session),
      soTiet: formData.periods,
      idPhong: 1,
      idMonHoc: 1,
      idHocKy: 1,
      idCanBo: 1,
      idLop: 1,
      idHinhThuc: getMasterDataIds.getStudyTypeId(formData.studyType),
      thuHoc: getMasterDataIds.getDayOfWeekNumber(formData.dayOfWeek),
      ngayHoc: formData.scheduleDate,
      ghiChu: formData.notes
    }
  }
}

// Helper functions to find IDs from master data
const findSubjectId = async (subjectName: string): Promise<number> => {
  try {
    const subjects = await masterDataService.getSubjects(0, 100) // Get more items
    const subject = subjects.content.find(s =>
      s.tenMonHoc.toLowerCase().includes(subjectName.toLowerCase()) ||
      s.maMonHoc.toLowerCase() === subjectName.toLowerCase()
    )
    return subject?.id || 1
  } catch (error) {
    console.error('Error finding subject ID:', error)
    return 1
  }
}

const findClassId = async (className: string): Promise<number> => {
  try {
    const classes = await masterDataService.getClasses(0, 100)
    const classItem = classes.content.find(c =>
      c.tenLop.toLowerCase() === className.toLowerCase() ||
      c.maLop.toLowerCase() === className.toLowerCase()
    )
    return classItem?.id || 1
  } catch (error) {
    console.error('Error finding class ID:', error)
    return 1
  }
}

const findRoomId = async (roomName: string): Promise<number> => {
  try {
    const rooms = await masterDataService.getRooms(0, 100)
    const room = rooms.content.find(r =>
      r.tenPhong.toLowerCase() === roomName.toLowerCase() ||
      r.maPhong.toLowerCase() === roomName.toLowerCase()
    )
    return room?.id || 1
  } catch (error) {
    console.error('Error finding room ID:', error)
    return 1
  }
}

const findTeacherId = async (teacherName: string): Promise<number> => {
  try {
    const teachers = await masterDataService.getTeachers(0, 100)
    const teacher = teachers.content.find(t =>
      t.tenCanBo.toLowerCase().includes(teacherName.toLowerCase()) ||
      t.maCanBo.toLowerCase() === teacherName.toLowerCase()
    )
    return teacher?.id || 1
  } catch (error) {
    console.error('Error finding teacher ID:', error)
    return 1
  }
}

// Convert CalendarEvent to Schedule format for frontend
function convertCalendarEventToSchedule(event: CalendarEvent): Schedule {
  const startDate = new Date(event.start)
  const endDate = new Date(event.end)
  const scheduleDate = event.start.split('T')[0] // Extract date part

  // Extract day of week in Vietnamese
  const dayOfWeek = getDayOfWeekFromDate(startDate)

  // Extract time parts
  const startTime = startDate.toTimeString().slice(0, 5) // HH:MM format
  const endTime = endDate.toTimeString().slice(0, 5) // HH:MM format

  // Determine lesson type from formType
  const lessonType = event.extendedProps.formType.includes('Lý thuyết') ? 'LT' : 'TH'

  return {
    id: event.id,
    subject: event.extendedProps.subjectName,
    class: event.extendedProps.className,
    teacher: event.extendedProps.teacherName,
    room: event.extendedProps.roomName,
    dayOfWeek: dayOfWeek,
    startTime: startTime,
    endTime: endTime,
    scheduleDate: scheduleDate,
    date: scheduleDate, // For calendar compatibility
    status: 'active', // Calendar events are typically active
    notes: event.extendedProps.note || '',
    periods: event.extendedProps.credits || 1,
    lessonType: lessonType,
    color: event.backgroundColor,
    startDate: scheduleDate,
    endDate: scheduleDate,
    // Additional fields from API
    subjectCode: event.extendedProps.subjectCode,
    classCode: event.extendedProps.classCode,
    campusName: event.extendedProps.campusName,
    sessionName: event.extendedProps.sessionName,
    practiceGroup: event.extendedProps.practiceGroup,
    credits: event.extendedProps.credits,
    coefficient: event.extendedProps.coefficient
  }
}

// Helper functions for data conversion
function getDayOfWeekName(thuHoc: number): string {
  const dayMap: { [key: number]: string } = {
    1: 'Chủ nhật',
    2: 'Thứ 2',
    3: 'Thứ 3',
    4: 'Thứ 4',
    5: 'Thứ 5',
    6: 'Thứ 6',
    7: 'Thứ 7'
  }
  return dayMap[thuHoc] || ''
}

function getDayOfWeekFromDate(date: Date): string {
  const dayMap: { [key: number]: string } = {
    0: 'Chủ nhật',
    1: 'Thứ 2',
    2: 'Thứ 3',
    3: 'Thứ 4',
    4: 'Thứ 5',
    5: 'Thứ 6',
    6: 'Thứ 7'
  }
  return dayMap[date.getDay()] || ''
}

function getTimeFromSession(idBuoi: number, type: 'start' | 'end'): string {
  const sessionMap: { [key: number]: { start: string; end: string } } = {
    1: { start: '06:00', end: '12:00' }, // Morning
    2: { start: '12:00', end: '18:00' }, // Afternoon
    3: { start: '18:00', end: '23:00' }  // Evening
  }
  return sessionMap[idBuoi]?.[type] || ''
}

// Schedule Service - updated to use correct backend format
export const scheduleService = {
  // Create schedule
  async createSchedule(formData: ScheduleFormData): Promise<Schedule> {
    const backendRequest = await convertToBackendRequest(formData)
    console.log('Sending to backend:', backendRequest) // Debug log

    const response = await api.post(API_CONFIG.ENDPOINTS.SCHEDULES.BASE, backendRequest)
    return convertBackendToFrontend(response.data || response)
  },

  // Update schedule
  async updateSchedule(id: number, formData: Partial<ScheduleFormData>): Promise<Schedule> {
    // For update, we need to convert partial data
    const fullFormData = formData as ScheduleFormData // Type assertion for now
    const backendRequest = await convertToBackendRequest(fullFormData)

    const response = await api.put(`${API_CONFIG.ENDPOINTS.SCHEDULES.BASE}/${id}`, backendRequest)
    return convertBackendToFrontend(response.data || response)
  },

  // Get schedule by ID
  async getScheduleById(id: number): Promise<Schedule> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.SCHEDULES.BASE}/${id}`)
    return convertBackendToFrontend(response.data || response)
  },

  // Delete schedule
  async deleteSchedule(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.SCHEDULES.BASE}/${id}`)
  },

  // Get all schedules with pagination (using calendar API)
  async getAllSchedules(params?: {
    page?: number;
    size?: number;
    sort?: string;
    semesterId?: number;
    teacherId?: number;
    classId?: number;
    departmentId?: number;
  }) {
    // For getting all schedules, we need to provide a date range
    // Default to current month if not specified
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)

    const calendarParams = {
      startDate: startOfMonth.toISOString().split('T')[0],
      endDate: endOfMonth.toISOString().split('T')[0],
      ...params
    }

    const response = await api.get(API_CONFIG.ENDPOINTS.SCHEDULES.CALENDAR, calendarParams)
    const apiResponse: ScheduleCalendarResponse = response.data || response

    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data.map(convertCalendarEventToSchedule)
    }

    return []
  },

  // Get schedules by teacher (using calendar API)
  async getSchedulesByTeacher(teacherId: number, semesterId?: number): Promise<Schedule[]> {
    // Default to current month for teacher schedules
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)

    const params = {
      startDate: startOfMonth.toISOString().split('T')[0],
      endDate: endOfMonth.toISOString().split('T')[0],
      teacherId,
      ...(semesterId && { semesterId })
    }

    const response = await api.get(API_CONFIG.ENDPOINTS.SCHEDULES.CALENDAR, params)
    const apiResponse: ScheduleCalendarResponse = response.data || response

    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data.map(convertCalendarEventToSchedule)
    }

    return []
  },

  // Get schedules by semester (using calendar API)
  async getSchedulesBySemester(semesterId: number, params?: {
    page?: number;
    size?: number;
    teacherId?: number;
    classId?: number;
    departmentId?: number;
  }): Promise<Schedule[]> {
    // Default to current month if no date range specified
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)

    const calendarParams = {
      startDate: startOfMonth.toISOString().split('T')[0],
      endDate: endOfMonth.toISOString().split('T')[0],
      semesterId,
      ...params
    }

    const response = await api.get(API_CONFIG.ENDPOINTS.SCHEDULES.CALENDAR, calendarParams)
    const apiResponse: ScheduleCalendarResponse = response.data || response

    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data.map(convertCalendarEventToSchedule)
    }

    return []
  },

  // Get schedules for calendar view (by date range) - Main calendar API method
  async getSchedulesForCalendar(params: {
    startDate: string;
    endDate: string;
    semesterId?: number;
    teacherId?: number;
    classId?: number;
    departmentId?: number;
  }): Promise<Schedule[]> {
    const response = await api.get(API_CONFIG.ENDPOINTS.SCHEDULES.CALENDAR, params)
    const apiResponse: ScheduleCalendarResponse = response.data || response

    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data.map(convertCalendarEventToSchedule)
    }

    return []
  },

  // Get raw calendar events (for calendar libraries that need the original format)
  async getCalendarEvents(params: {
    startDate: string;
    endDate: string;
    semesterId?: number;
    teacherId?: number;
    classId?: number;
    departmentId?: number;
  }): Promise<CalendarEvent[]> {
    const response = await api.get(API_CONFIG.ENDPOINTS.SCHEDULES.CALENDAR, params)
    const apiResponse: ScheduleCalendarResponse = response.data || response

    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data
    }

    return []
  }
}
