'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  GraduationCap,
  Menu,
  LogOut,
  Settings,
  User,
  Home,
  Calendar,
  Clock,
  FileText,
  Database,
  Building,
  BookOpen,
  Users,
  BarChart3,
  Shield,
  X
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface UserInfo {
  id: number
  maCanBo: string
  tenCanBo: string
  email: string
  soDienThoai: string
  vaiTro: string
  tenKhoa: string
  nu: boolean
}

interface NavigationItem {
  title: string
  href: string
  icon: any
  adminOnly?: boolean
  teacherOnly?: boolean
}

const navigation: NavigationItem[] = [
  { title: 'Dashboard', href: '/admin', icon: Home, adminOnly: true },
  { title: 'Dashboard', href: '/teacher', icon: Home, teacherOnly: true },
  { title: 'Năm học & Học kỳ', href: '/admin/academic-years', icon: Calendar, adminOnly: true },
  { title: 'Dữ liệu Danh mục', href: '/admin/master-data', icon: Database, adminOnly: true },
  { title: 'Import & Đồng bộ', href: '/admin/import-management', icon: FileText, adminOnly: true },
  { title: 'Thống kê & Báo cáo', href: '/admin/reports', icon: BarChart3, adminOnly: true },
  { title: 'Lịch giảng', href: '/teacher/schedules', icon: Calendar, teacherOnly: true },
  { title: 'Giờ giảng dạy', href: '/teacher/teaching-hours', icon: Clock, teacherOnly: true },
  { title: 'Báo cáo', href: '/teacher/reports', icon: FileText, teacherOnly: true },
  { title: 'Cài đặt', href: '/teacher/settings', icon: Settings, teacherOnly: true },
]

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  // Xác định role dựa trên pathname
  const isAdminPage = pathname.startsWith('/admin')
  const isTeacherPage = pathname.startsWith('/teacher')

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr && userInfoStr !== 'undefined') {
      try {
        const user = JSON.parse(userInfoStr)
        setUserInfo(user)
      } catch (error) {
        console.error('Error parsing user info:', error)
        router.push('/login')
      }
    } else {
      router.push('/login')
    }
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    // Xóa cookies
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
    document.cookie = 'userInfo=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
    router.push('/login')
  }

  const isAdmin = userInfo?.vaiTro === 'Admin' || userInfo?.vaiTro === 'ADMIN' || userInfo?.vaiTro === 'QUAN_TRI'

  const getRoleInfo = () => {
    if (!userInfo) return { icon: User, color: 'text-gray-600', badge: 'User', badgeColor: 'bg-gray-100' }

    if (isAdmin) {
      return {
        icon: Shield,
        color: 'text-blue-600',
        badge: 'Quản trị viên',
        badgeColor: 'bg-blue-600 text-white'
      }
    } else {
      return {
        icon: User,
        color: 'text-green-600',
        badge: 'Giảng viên',
        badgeColor: 'bg-green-100 text-green-800'
      }
    }
  }

  const getPageTitle = () => {
    if (isAdminPage) return 'Bảng điều khiển quản trị'
    if (isTeacherPage) return 'Dành cho giảng viên'
    return 'Hệ thống quản lý lịch giảng'
  }

  const filteredNavigation = navigation.filter(item => {
    if (item.adminOnly && !isAdmin) return false
    if (item.teacherOnly && isAdmin) return false
    return true
  })

  if (!userInfo) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const roleInfo = getRoleInfo()
  const RoleIcon = roleInfo.icon

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Main Header Row */}
          <div className="flex items-center justify-between h-16">
            {/* Left side: Logo và Title */}
            <div className="flex items-center min-w-0 flex-shrink-0">
              <GraduationCap className={`h-8 w-8 ${roleInfo.color} mr-3`} />
              <div className="hidden lg:block">
                <h1 className="text-xl font-semibold text-gray-900">
                  Hệ thống quản lý lịch giảng
                </h1>
                <p className="text-xs text-gray-500">
                  {getPageTitle()}
                </p>
              </div>
              <div className="lg:hidden">
                <h1 className="text-lg font-semibold text-gray-900">
                  QLGD
                </h1>
              </div>
            </div>

            {/* Right side: User Info */}
            <div className="flex items-center space-x-4">
              {/* Desktop User Avatar Dropdown */}
              <div className="hidden md:block">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="" alt={userInfo.tenCanBo} />
                        <AvatarFallback className={`${roleInfo.color} bg-opacity-10`}>
                          {userInfo.tenCanBo.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{userInfo.tenCanBo}</p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {userInfo.email}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className={`${roleInfo.badgeColor} text-xs`}>
                            {roleInfo.badge}
                          </Badge>
                          {userInfo.tenKhoa && (
                            <span className="text-xs text-muted-foreground">
                              {userInfo.tenKhoa}
                            </span>
                          )}
                        </div>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => router.push('/profile')}>
                      <User className="mr-2 h-4 w-4" />
                      <span>Thông tin cá nhân</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => router.push(isAdmin ? '/admin/settings' : '/teacher/settings')}>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Cài đặt</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Đăng xuất</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Mobile Menu Button */}
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? (
                  <X className="h-4 w-4" />
                ) : (
                  <Menu className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Navigation Row */}
          <div className="hidden md:block border-t bg-gray-50 py-2">
            <nav className="flex items-center justify-center space-x-2 overflow-x-auto">
              {filteredNavigation.map((item) => (
                <Button
                  key={item.href}
                  variant={pathname === item.href ? "default" : "ghost"}
                  size="sm"
                  onClick={() => router.push(item.href)}
                  className="flex items-center gap-2 text-sm px-4 py-2 whitespace-nowrap"
                >
                  <item.icon className="h-4 w-4" />
                  {item.title}
                </Button>
              ))}
            </nav>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden border-t bg-white py-4">
              <div className="space-y-3">
                {/* Mobile User Info with Avatar */}
                <div className="flex items-center space-x-3 px-4 py-2 bg-gray-50 rounded-lg mx-2">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src="" alt={userInfo.tenCanBo} />
                    <AvatarFallback className={`${roleInfo.color} bg-opacity-10`}>
                      {userInfo.tenCanBo.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {userInfo.tenCanBo}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {userInfo.email}
                    </p>
                    <Badge className={`${roleInfo.badgeColor} text-xs mt-1`}>
                      {roleInfo.badge}
                    </Badge>
                  </div>
                </div>

                {/* Mobile Navigation */}
                <div className="px-2 space-y-2">
                  {filteredNavigation.map((item) => (
                    <Button
                      key={item.href}
                      variant={pathname === item.href ? "default" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => {
                        router.push(item.href)
                        setMobileMenuOpen(false)
                      }}
                    >
                      <item.icon className="mr-2 h-4 w-4" />
                      {item.title}
                    </Button>
                  ))}
                </div>

                {/* Mobile Menu Actions */}
                <div className="px-2 space-y-2 border-t pt-3">
                  <Button
                    variant="ghost"
                    className="w-full justify-start"
                    onClick={() => {
                      router.push('/profile')
                      setMobileMenuOpen(false)
                    }}
                  >
                    <User className="mr-2 h-4 w-4" />
                    Thông tin cá nhân
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start"
                    onClick={() => {
                      router.push(isAdmin ? '/admin/settings' : '/teacher/settings')
                      setMobileMenuOpen(false)
                    }}
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    Cài đặt
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleLogout}
                    className="w-full"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Đăng xuất
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 p-4 md:p-6 max-w-7xl mx-auto">
        {children}
      </main>
    </div>
  )
}
