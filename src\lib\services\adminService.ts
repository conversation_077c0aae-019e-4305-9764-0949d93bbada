import { api } from '@/lib/utils/apiClient'
import { API_CONFIG } from '@/lib/config/api'
import { withFallback } from '@/lib/utils/errorHandler'
import { withCache, CACHE_KEYS } from '@/lib/utils/apiCache'
import { shouldUseMockData, mockDelay, devLog } from '@/lib/config/development'

export interface DashboardStats {
  totalTeachers: number
  totalSchedules: number
  totalSubjects: number
  totalClasses: number
  totalRooms: number
  totalDepartments: number
  currentSemester: string
  weeklySchedules: number
}

export interface SystemInfo {
  version: string
  environment: string
  database: {
    type: string
    version: string
    size: string
  }
  server: {
    memory: string
    cpu: string
    uptime: string
  }
  dataStatistics: {
    totalUsers: number
    totalSchedules: number
    totalSubjects: number
    totalClasses: number
    totalRooms: number
    totalDepartments: number
  }
}

export interface SystemStatistics {
  userStatistics: {
    totalUsers: number
    activeUsers: number
    adminUsers: number
    teacherUsers: number
  }
  scheduleStatistics: {
    totalSchedules: number
    approvedSchedules: number
    pendingSchedules: number
    rejectedSchedules: number
  }
  dataStatistics: {
    totalSubjects: number
    totalClasses: number
    totalRooms: number
    totalDepartments: number
  }
  systemHealth: {
    status: 'healthy' | 'warning' | 'error'
    uptime: string
    memoryUsage: number
    diskUsage: number
  }
}

export interface BackupInfo {
  id: string
  fileName: string
  size: number
  createdAt: string
  type: 'manual' | 'auto'
  status: 'completed' | 'failed' | 'in_progress'
}

class AdminService {
  // Lấy dữ liệu dashboard
  async getDashboardData(): Promise<DashboardStats> {
    const fallbackData: DashboardStats = {
      totalTeachers: 156,
      totalSchedules: 342,
      totalSubjects: 245,
      totalClasses: 89,
      totalRooms: 67,
      totalDepartments: 12,
      currentSemester: 'Học kỳ 1 - 2024-2025',
      weeklySchedules: 28
    }

    // If in development mode and mock data is enabled, return mock data directly
    if (shouldUseMockData()) {
      devLog.info('Using mock dashboard data (development mode)')
      await mockDelay()
      return fallbackData
    }

    return withCache(
      CACHE_KEYS.DASHBOARD_DATA,
      () => withFallback(
        async () => {
          const response = await api.get(API_CONFIG.ENDPOINTS.ADMIN.DASHBOARD)
          return response.data
        },
        fallbackData,
        'Dashboard data'
      ),
      2 * 60 * 1000 // Cache for 2 minutes
    )
  }

  // Lấy thông tin hệ thống
  async getSystemInfo(): Promise<SystemInfo> {
    const response = await api.get('/api/admin/system-info')
    return response.data
  }

  // Lấy thống kê hệ thống
  async getSystemStatistics(): Promise<SystemStatistics> {
    const response = await api.get('/api/admin/statistics')
    return response.data
  }

  // Khởi tạo dữ liệu mẫu
  async initializeData(): Promise<string> {
    const response = await api.post(API_CONFIG.ENDPOINTS.ADMIN.INITIALIZE)
    return response.message
  }

  // Tạo backup
  async createBackup(): Promise<string> {
    const response = await api.post(API_CONFIG.ENDPOINTS.ADMIN.BACKUP)
    return response.data
  }

  // Lấy danh sách backup
  async getBackupList(): Promise<BackupInfo[]> {
    const response = await api.get('/api/admin/backups')
    return response.data
  }

  // Xóa backup
  async deleteBackup(backupId: string): Promise<void> {
    await api.delete(`/api/admin/backups/${backupId}`)
  }

  // Restore từ backup
  async restoreBackup(backupId: string): Promise<string> {
    const response = await api.post(`/api/admin/backups/${backupId}/restore`)
    return response.message
  }

  // Kiểm tra tình trạng hệ thống
  async checkSystemHealth(): Promise<any> {
    const response = await api.get('/api/admin/health')
    return response.data
  }

  // Dọn dẹp dữ liệu cũ
  async cleanupOldData(): Promise<string> {
    const response = await api.post('/api/admin/cleanup')
    return response.message
  }

  // Tối ưu hóa database
  async optimizeDatabase(): Promise<string> {
    const response = await api.post('/api/admin/optimize')
    return response.message
  }

  // Lấy logs hệ thống
  async getSystemLogs(level?: string, limit?: number): Promise<any[]> {
    const params: Record<string, any> = {}
    if (level) params.level = level
    if (limit) params.limit = limit

    const response = await api.get('/api/admin/logs', params)
    return response.data
  }

  // Cập nhật cấu hình hệ thống
  async updateSystemConfig(config: Record<string, any>): Promise<void> {
    await api.put('/api/admin/config', config)
  }

  // Lấy cấu hình hệ thống
  async getSystemConfig(): Promise<Record<string, any>> {
    const response = await api.get('/api/admin/config')
    return response.data
  }

  // Gửi thông báo hệ thống
  async sendSystemNotification(message: string, type: 'info' | 'warning' | 'error'): Promise<void> {
    await api.post('/api/admin/notifications', { message, type })
  }

  // Lấy thống kê sử dụng theo thời gian
  async getUsageStatistics(startDate: string, endDate: string): Promise<any> {
    const response = await api.get('/api/admin/usage-stats', { start: startDate, end: endDate })
    return response.data
  }
}

export const adminService = new AdminService()
