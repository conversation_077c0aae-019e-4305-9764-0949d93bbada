'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  RefreshCw,
  Upload,
  Download,
  Trash2,
  Settings,
  MoreHorizontal,
  CheckCircle,
  AlertCircle,
  Clock,
  Database,
  FileSpreadsheet,
  Users,
  BookOpen,
  MapPin,
  Calendar
} from 'lucide-react'
import { importService, ImportStatus } from '@/lib/services/importService'
import { formatDate } from '@/lib/utils'

interface ImportDataTableProps {
  onUpload?: (type: string) => void
  onSync?: (type: string) => void
  onConfigure?: (type: string) => void
}

const DATA_TYPE_CONFIG = {
  subjects: {
    name: 'Môn học',
    icon: BookOpen,
    color: 'bg-blue-500'
  },
  classes: {
    name: 'Lớp học',
    icon: Users,
    color: 'bg-green-500'
  },
  teachers: {
    name: 'Giảng viên',
    icon: Users,
    color: 'bg-purple-500'
  },
  rooms: {
    name: 'Phòng học',
    icon: MapPin,
    color: 'bg-orange-500'
  },
  academic_years: {
    name: 'Năm học/Học kỳ',
    icon: Calendar,
    color: 'bg-red-500'
  }
}

export default function ImportDataTable({ onUpload, onSync, onConfigure }: ImportDataTableProps) {
  const [importStatuses, setImportStatuses] = useState<ImportStatus[]>([])
  const [loading, setLoading] = useState(false)
  const [syncingTypes, setSyncingTypes] = useState<Set<string>>(new Set())
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadImportStatuses()
  }, [])

  const loadImportStatuses = async () => {
    try {
      setLoading(true)
      setError(null)
      const statuses = await importService.getImportStatus()
      setImportStatuses(statuses)
    } catch (err) {
      setError('Không thể tải trạng thái import')
      console.error('Error loading import statuses:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSync = async (type: string) => {
    try {
      setSyncingTypes(prev => new Set(prev).add(type))
      setError(null)
      
      await importService.syncData(type)
      await loadImportStatuses() // Reload to get updated status
      
      if (onSync) {
        onSync(type)
      }
    } catch (err) {
      setError(`Lỗi đồng bộ ${DATA_TYPE_CONFIG[type as keyof typeof DATA_TYPE_CONFIG]?.name}: ${err}`)
    } finally {
      setSyncingTypes(prev => {
        const newSet = new Set(prev)
        newSet.delete(type)
        return newSet
      })
    }
  }

  const handleSyncAll = async () => {
    try {
      setLoading(true)
      setError(null)
      
      await importService.syncAllData()
      await loadImportStatuses()
    } catch (err) {
      setError(`Lỗi đồng bộ tất cả: ${err}`)
    } finally {
      setLoading(false)
    }
  }

  const handleClearData = async (type: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa tất cả dữ liệu ${DATA_TYPE_CONFIG[type as keyof typeof DATA_TYPE_CONFIG]?.name}?`)) {
      return
    }

    try {
      await importService.clearData(type)
      await loadImportStatuses()
    } catch (err) {
      setError(`Lỗi xóa dữ liệu: ${err}`)
    }
  }

  const handleDownloadTemplate = async (type: string) => {
    try {
      const blob = await importService.downloadTemplate(type)
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `template_${type}.xlsx`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (err) {
      setError(`Lỗi tải template: ${err}`)
    }
  }

  const getStatusBadge = (status: ImportStatus['status']) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Thành công</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="w-3 h-3 mr-1" />Lỗi</Badge>
      case 'syncing':
        return <Badge className="bg-blue-100 text-blue-800"><RefreshCw className="w-3 h-3 mr-1 animate-spin" />Đang đồng bộ</Badge>
      case 'pending':
        return <Badge className="bg-gray-100 text-gray-800"><Clock className="w-3 h-3 mr-1" />Chờ đồng bộ</Badge>
      default:
        return <Badge variant="secondary">Không xác định</Badge>
    }
  }

  if (loading && importStatuses.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <RefreshCw className="w-6 h-6 animate-spin mr-2" />
          Đang tải...
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Database className="w-5 h-5 mr-2" />
                Quản lý Import & Đồng bộ dữ liệu
              </CardTitle>
              <CardDescription>
                Quản lý việc import và đồng bộ dữ liệu từ các hệ thống bên ngoài
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button onClick={loadImportStatuses} variant="outline" size="sm">
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Làm mới
              </Button>
              <Button onClick={handleSyncAll} disabled={loading} size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Đồng bộ tất cả
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Loại dữ liệu</TableHead>
                <TableHead>Trạng thái</TableHead>
                <TableHead>Số lượng</TableHead>
                <TableHead>Lần đồng bộ cuối</TableHead>
                <TableHead>Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {importStatuses.map((status) => {
                const config = DATA_TYPE_CONFIG[status.type as keyof typeof DATA_TYPE_CONFIG]
                const Icon = config?.icon || Database
                const isSyncing = syncingTypes.has(status.type)
                
                return (
                  <TableRow key={status.type}>
                    <TableCell>
                      <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full ${config?.color || 'bg-gray-500'} flex items-center justify-center mr-3`}>
                          <Icon className="w-4 h-4 text-white" />
                        </div>
                        <span className="font-medium">{config?.name || status.type}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {isSyncing ? (
                        <Badge className="bg-blue-100 text-blue-800">
                          <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                          Đang đồng bộ
                        </Badge>
                      ) : (
                        getStatusBadge(status.status)
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="font-mono">{status.count.toLocaleString()}</span>
                    </TableCell>
                    <TableCell>
                      {status.lastSync ? formatDate(status.lastSync) : 'Chưa đồng bộ'}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          onClick={() => handleSync(status.type)}
                          disabled={isSyncing || loading}
                          size="sm"
                          variant="outline"
                        >
                          <RefreshCw className={`w-3 h-3 mr-1 ${isSyncing ? 'animate-spin' : ''}`} />
                          Đồng bộ
                        </Button>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onUpload?.(status.type)}>
                              <Upload className="w-4 h-4 mr-2" />
                              Upload file
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDownloadTemplate(status.type)}>
                              <Download className="w-4 h-4 mr-2" />
                              Tải template
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onConfigure?.(status.type)}>
                              <Settings className="w-4 h-4 mr-2" />
                              Cấu hình
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleClearData(status.type)}
                              className="text-red-600"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Xóa dữ liệu
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
