'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { scheduleService } from '@/lib/services/scheduleService'

export default function ScheduleAPIDebug() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [params, setParams] = useState({
    startDate: '2024-12-01',
    endDate: '2024-12-31',
    semesterId: 1
  })

  const testAPI = async () => {
    try {
      setLoading(true)
      setError(null)
      setResult(null)

      console.log('🧪 Testing Schedule API with params:', params)

      // Test the calendar API directly
      const schedules = await scheduleService.getSchedulesForCalendar(params)
      
      console.log('✅ API test completed, result:', schedules)
      setResult({
        success: true,
        schedulesCount: schedules.length,
        schedules: schedules,
        timestamp: new Date().toISOString()
      })

    } catch (err) {
      console.error('❌ API test failed:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      setResult({
        success: false,
        error: err,
        timestamp: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  const testRawAPI = async () => {
    try {
      setLoading(true)
      setError(null)
      setResult(null)

      console.log('🧪 Testing Raw Calendar Events API with params:', params)

      // Test the raw calendar events API
      const events = await scheduleService.getCalendarEvents(params)
      
      console.log('✅ Raw API test completed, result:', events)
      setResult({
        success: true,
        eventsCount: events.length,
        events: events,
        timestamp: new Date().toISOString()
      })

    } catch (err) {
      console.error('❌ Raw API test failed:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      setResult({
        success: false,
        error: err,
        timestamp: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>🧪 Schedule API Debug Tool</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                value={params.startDate}
                onChange={(e) => setParams(prev => ({ ...prev, startDate: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                value={params.endDate}
                onChange={(e) => setParams(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="semesterId">Semester ID</Label>
              <Input
                id="semesterId"
                type="number"
                value={params.semesterId}
                onChange={(e) => setParams(prev => ({ ...prev, semesterId: parseInt(e.target.value) }))}
              />
            </div>
          </div>

          <div className="flex gap-4">
            <Button 
              onClick={testAPI} 
              disabled={loading}
              className="flex-1"
            >
              {loading ? 'Testing...' : 'Test Converted Schedules API'}
            </Button>
            <Button 
              onClick={testRawAPI} 
              disabled={loading}
              variant="outline"
              className="flex-1"
            >
              {loading ? 'Testing...' : 'Test Raw Calendar Events API'}
            </Button>
          </div>

          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <h3 className="font-medium text-red-800">Error:</h3>
              <p className="text-red-600">{error}</p>
            </div>
          )}

          {result && (
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                <h3 className="font-medium text-blue-800">Result Summary:</h3>
                <pre className="text-sm text-blue-600 mt-2">
                  {JSON.stringify({
                    success: result.success,
                    schedulesCount: result.schedulesCount,
                    eventsCount: result.eventsCount,
                    timestamp: result.timestamp
                  }, null, 2)}
                </pre>
              </div>

              <div className="p-4 bg-gray-50 border border-gray-200 rounded-md max-h-96 overflow-auto">
                <h3 className="font-medium text-gray-800 mb-2">Full Response:</h3>
                <pre className="text-xs text-gray-600">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>📋 Debug Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>1. Open Browser DevTools</strong> (F12) and go to Console tab</p>
            <p><strong>2. Set date range</strong> that should contain schedule data</p>
            <p><strong>3. Click "Test" buttons</strong> to see detailed API logs</p>
            <p><strong>4. Check console logs</strong> for detailed debugging information:</p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>🚀 API call parameters</li>
              <li>📡 API endpoint being called</li>
              <li>📥 Raw API response</li>
              <li>🔄 Data conversion process</li>
              <li>✅ Final converted schedules</li>
            </ul>
            <p><strong>5. Compare results</strong> between raw events and converted schedules</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
