// Simple in-memory cache for API responses
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number // Time to live in milliseconds
}

class ApiCache {
  private cache = new Map<string, CacheEntry<any>>()
  private defaultTTL = 5 * 60 * 1000 // 5 minutes

  set<T>(key: string, data: T, ttl?: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    })
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }

    // Check if cache entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data as T
  }

  has(key: string): boolean {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return false
    }

    // Check if cache entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  // Get cache stats
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// Singleton instance
export const apiCache = new ApiCache()

// Wrapper function for caching API calls
export async function withCache<T>(
  key: string,
  apiCall: () => Promise<T>,
  ttl?: number
): Promise<T> {
  // Check cache first
  const cached = apiCache.get<T>(key)
  if (cached !== null) {
    console.info(`📦 Using cached data for ${key}`)
    return cached
  }

  // Make API call and cache result
  try {
    const data = await apiCall()
    apiCache.set(key, data, ttl)
    return data
  } catch (error) {
    // Don't cache errors
    throw error
  }
}

// Cache keys constants
export const CACHE_KEYS = {
  DASHBOARD_DATA: 'dashboard_data',
  MASTER_DATA_STATS: 'master_data_stats',
  USER_INFO: 'user_info',
  SYSTEM_INFO: 'system_info'
} as const

export default apiCache
