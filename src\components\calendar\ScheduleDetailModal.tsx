'use client'

import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Calendar,
  Clock,
  MapPin,
  User,
  BookOpen,
  Edit,
  Trash2,
  Copy
} from 'lucide-react'

interface Schedule {
  id: number
  subject: string
  class: string
  teacher: string
  room: string
  startTime: string
  endTime: string
  date: string
  status: 'confirmed' | 'pending' | 'cancelled'
  color: string
  notes?: string
  semester?: string
  academicYear?: string
}

interface ScheduleDetailModalProps {
  schedule: Schedule | null
  isOpen: boolean
  onClose: () => void
  onEdit?: (schedule: Schedule) => void
  onDelete?: (scheduleId: number) => void
  onDuplicate?: (schedule: Schedule) => void
  isAdmin?: boolean
}

export default function ScheduleDetailModal({
  schedule,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  onDuplicate,
  isAdmin = false
}: ScheduleDetailModalProps) {
  const [isDeleting, setIsDeleting] = useState(false)

  if (!schedule) return null

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <Badge variant="default">Đã xác nhận</Badge>
      case 'pending':
        return <Badge variant="secondary">Chờ duyệt</Badge>
      case 'cancelled':
        return <Badge variant="destructive">Đã hủy</Badge>
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const handleDelete = async () => {
    if (!confirm('Bạn có chắc chắn muốn xóa lịch giảng này?')) return

    setIsDeleting(true)
    try {
      await onDelete?.(schedule.id)
      onClose()
    } catch (error) {
      console.error('Error deleting schedule:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <DialogTitle className="text-xl font-bold">
                {schedule.subject}
              </DialogTitle>
              <DialogDescription>
                Chi tiết lịch giảng
              </DialogDescription>
            </div>
            <div className="flex items-center">
              {getStatusBadge(schedule.status)}
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <BookOpen className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Lớp học</p>
                  <p className="font-medium">{schedule.class}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Giảng viên</p>
                  <p className="font-medium">{schedule.teacher}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <MapPin className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Phòng học</p>
                  <p className="font-medium">{schedule.room}</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Ngày học</p>
                  <p className="font-medium">{formatDate(schedule.date)}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Clock className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Thời gian</p>
                  <p className="font-medium">{schedule.startTime} - {schedule.endTime}</p>
                </div>
              </div>

              {schedule.academicYear && (
                <div>
                  <p className="text-sm text-gray-500">Năm học</p>
                  <p className="font-medium">{schedule.academicYear}</p>
                </div>
              )}

              {schedule.semester && (
                <div>
                  <p className="text-sm text-gray-500">Học kỳ</p>
                  <p className="font-medium">{schedule.semester}</p>
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          {schedule.notes && (
            <>
              <Separator />
              <div>
                <p className="text-sm text-gray-500 mb-2">Ghi chú</p>
                <p className="text-sm bg-gray-50 p-3 rounded-md">{schedule.notes}</p>
              </div>
            </>
          )}

          {/* Actions */}
          <Separator />
          <div className="flex justify-between">
            <div className="flex space-x-2">
              {isAdmin && (
                <>
                  <Button
                    variant="outline"
                    onClick={() => onEdit?.(schedule)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Chỉnh sửa
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => onDuplicate?.(schedule)}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Nhân bản
                  </Button>
                </>
              )}
            </div>

            <div className="flex space-x-2">
              {isAdmin && (
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={isDeleting}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {isDeleting ? 'Đang xóa...' : 'Xóa'}
                </Button>
              )}
              <Button variant="outline" onClick={onClose}>
                Đóng
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
