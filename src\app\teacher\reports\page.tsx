"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft,
  Download,
  FileText,
  Calendar,
  Clock,
  BarChart3,
  Filter
} from "lucide-react"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"

interface ReportData {
  totalHours: number
  totalSchedules: number
  completedSchedules: number
  pendingSchedules: number
  monthlyHours: { month: string; hours: number }[]
  subjectHours: { subject: string; hours: number }[]
}

export default function TeacherReports() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState("this-month")
  const [selectedFormat, setSelectedFormat] = useState("excel")

  useEffect(() => {
    // Kiểm tra quyền truy cập
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    // Mock data
    setReportData({
      totalHours: 120,
      totalSchedules: 45,
      completedSchedules: 42,
      pendingSchedules: 3,
      monthlyHours: [
        { month: "Tháng 1", hours: 32 },
        { month: "Tháng 2", hours: 28 },
        { month: "Tháng 3", hours: 35 },
        { month: "Tháng 4", hours: 25 }
      ],
      subjectHours: [
        { subject: "Lập trình Java", hours: 45 },
        { subject: "Cơ sở dữ liệu", hours: 38 },
        { subject: "Mạng máy tính", hours: 37 }
      ]
    })
  }, [router])

  const handleExportReport = async () => {
    setLoading(true)
    
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast({
        title: "Thành công",
        description: `Báo cáo ${selectedFormat.toUpperCase()} đã được tạo và tải xuống`
      })
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể tạo báo cáo",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const reportTypes = [
    {
      title: "Báo cáo giờ giảng",
      description: "Tổng hợp giờ giảng theo thời gian",
      icon: Clock,
      color: "bg-blue-500"
    },
    {
      title: "Báo cáo lịch giảng",
      description: "Chi tiết các buổi giảng đã thực hiện",
      icon: Calendar,
      color: "bg-green-500"
    },
    {
      title: "Báo cáo theo môn học",
      description: "Phân tích giờ giảng theo từng môn",
      icon: BarChart3,
      color: "bg-purple-500"
    },
    {
      title: "Báo cáo tổng hợp",
      description: "Báo cáo tổng quan tất cả hoạt động",
      icon: FileText,
      color: "bg-orange-500"
    }
  ]

  if (!reportData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/teacher')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Báo Cáo</h1>
            <p className="text-muted-foreground">
              Xuất báo cáo và thống kê hoạt động giảng dạy
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Bộ lọc
          </Button>
        </div>
      </div>

      {/* Statistics Overview */}
      <div className="grid gap-4 md:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng giờ giảng</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reportData.totalHours}</div>
            <p className="text-xs text-muted-foreground">
              Tất cả thời gian
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng lịch giảng</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reportData.totalSchedules}</div>
            <p className="text-xs text-muted-foreground">
              Đã lên lịch
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đã hoàn thành</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reportData.completedSchedules}</div>
            <p className="text-xs text-muted-foreground">
              Buổi giảng
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chờ thực hiện</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reportData.pendingSchedules}</div>
            <p className="text-xs text-muted-foreground">
              Buổi giảng
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Export Options */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Xuất Báo Cáo</CardTitle>
          <CardDescription>
            Chọn loại báo cáo và định dạng để xuất
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">Thời gian</label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="this-week">Tuần này</SelectItem>
                  <SelectItem value="this-month">Tháng này</SelectItem>
                  <SelectItem value="this-semester">Học kỳ này</SelectItem>
                  <SelectItem value="this-year">Năm học này</SelectItem>
                  <SelectItem value="custom">Tùy chọn</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Định dạng</label>
              <Select value={selectedFormat} onValueChange={setSelectedFormat}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                  <SelectItem value="pdf">PDF</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button 
                onClick={handleExportReport} 
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Đang tạo...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Xuất báo cáo
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Types */}
      <div className="grid gap-6 md:grid-cols-2">
        {reportTypes.map((report, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className={`w-12 h-12 ${report.color} rounded-lg flex items-center justify-center`}>
                  <report.icon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-lg">{report.title}</CardTitle>
                  <CardDescription>{report.description}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full">
                <Download className="h-4 w-4 mr-2" />
                Tạo báo cáo
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Stats */}
      <div className="grid gap-6 md:grid-cols-2 mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Giờ giảng theo tháng</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {reportData.monthlyHours.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm">{item.month}</span>
                  <Badge variant="outline">{item.hours} giờ</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Giờ giảng theo môn học</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {reportData.subjectHours.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm">{item.subject}</span>
                  <Badge variant="outline">{item.hours} giờ</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  )
}
