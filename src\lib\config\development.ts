// Development configuration
export const DEV_CONFIG = {
  // API Configuration
  USE_MOCK_DATA: process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true',
  DISABLE_API_CALLS: process.env.NEXT_PUBLIC_DISABLE_API_CALLS === 'true',
  
  // Logging Configuration
  VERBOSE_LOGGING: process.env.NODE_ENV === 'development',
  SHOW_API_ERRORS: process.env.NODE_ENV === 'development',
  
  // Cache Configuration
  CACHE_TTL: process.env.NODE_ENV === 'development' ? 30 * 1000 : 5 * 60 * 1000, // 30s in dev, 5min in prod
  
  // Mock Data Configuration
  MOCK_DELAY: parseInt(process.env.NEXT_PUBLIC_MOCK_DELAY || '500'), // Simulate network delay
} as const

// Helper function to check if we should use mock data
export function shouldUseMockData(): boolean {
  return DEV_CONFIG.USE_MOCK_DATA || DEV_CONFIG.DISABLE_API_CALLS
}

// Helper function to simulate network delay in development
export function mockDelay(ms?: number): Promise<void> {
  if (process.env.NODE_ENV !== 'development') {
    return Promise.resolve()
  }
  
  const delay = ms || DEV_CONFIG.MOCK_DELAY
  return new Promise(resolve => setTimeout(resolve, delay))
}

// Development logger
export const devLog = {
  info: (message: string, ...args: any[]) => {
    if (DEV_CONFIG.VERBOSE_LOGGING) {
      console.info(`🔧 [DEV] ${message}`, ...args)
    }
  },
  
  warn: (message: string, ...args: any[]) => {
    if (DEV_CONFIG.VERBOSE_LOGGING) {
      console.warn(`⚠️ [DEV] ${message}`, ...args)
    }
  },
  
  error: (message: string, ...args: any[]) => {
    if (DEV_CONFIG.SHOW_API_ERRORS) {
      console.error(`❌ [DEV] ${message}`, ...args)
    }
  },
  
  api: (endpoint: string, method: string, status?: number) => {
    if (DEV_CONFIG.VERBOSE_LOGGING) {
      const statusText = status ? `(${status})` : ''
      console.info(`🌐 [API] ${method} ${endpoint} ${statusText}`)
    }
  }
}

export default DEV_CONFIG
