"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Calendar,
  Clock,
  ArrowLeft,
  Filter,
  Search,
  FileText,
  Download,
  CalendarDays,
  Grid
} from "lucide-react"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import ScheduleCalendar from '@/components/calendar/ScheduleCalendar'
import ScheduleDetailModal from '@/components/calendar/ScheduleDetailModal'
import { scheduleService } from '@/lib/services/scheduleService'

interface Schedule {
  id: number
  subject: string
  class: string
  room: string
  dayOfWeek: string
  startTime: string
  endTime: string
  status: 'active' | 'pending' | 'cancelled'
}

export default function TeacherSchedules() {
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSchedule, setSelectedSchedule] = useState<Schedule | null>(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('calendar')
  const router = useRouter()

  useEffect(() => {
    // Kiểm tra quyền truy cập
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    loadTeacherSchedules()
  }, [router])

  const loadTeacherSchedules = async () => {
    try {
      setLoading(true)

      // Get teacher ID from localStorage or user context
      // For now, using a mock teacher ID - this should come from user authentication
      const teacherId = 1 // TODO: Get from authenticated user context

      // Load teacher's schedules
      const schedulesData = await scheduleService.getSchedulesByTeacher(teacherId)
      setSchedules(schedulesData)

    } catch (error) {
      console.error('Error loading teacher schedules:', error)
      // Fallback to empty array on error
      setSchedules([])
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Đang hoạt động</Badge>
      case 'pending':
        return <Badge variant="secondary">Chờ duyệt</Badge>
      case 'cancelled':
        return <Badge variant="destructive">Đã hủy</Badge>
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  const filteredSchedules = schedules.filter(schedule =>
    schedule.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    schedule.class.toLowerCase().includes(searchTerm.toLowerCase()) ||
    schedule.room.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleScheduleClick = (schedule: Schedule) => {
    setSelectedSchedule(schedule)
    setIsDetailModalOpen(true)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/teacher')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Lịch giảng của tôi
            </h1>
            <p className="text-muted-foreground">
              Xem lịch giảng dạy được phân công
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => window.print()}>
            <FileText className="h-4 w-4 mr-2" />
            In lịch
          </Button>
          <Button variant="outline" onClick={() => router.push('/teacher/schedules/export')}>
            <Download className="h-4 w-4 mr-2" />
            Xuất Excel
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
          <TabsTrigger value="calendar" className="flex items-center">
            <CalendarDays className="h-4 w-4 mr-2" />
            Lịch
          </TabsTrigger>
          <TabsTrigger value="list" className="flex items-center">
            <Grid className="h-4 w-4 mr-2" />
            Danh sách
          </TabsTrigger>
        </TabsList>

        {/* Calendar View */}
        <TabsContent value="calendar" className="space-y-6">
          <ScheduleCalendar
            onScheduleClick={handleScheduleClick}
            isAdmin={false}
          />
        </TabsContent>

        {/* List View */}
        <TabsContent value="list" className="space-y-6">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Tìm kiếm theo môn học, lớp, phòng..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Bộ lọc
            </Button>
          </div>

      {/* Schedules Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredSchedules.map((schedule) => (
          <Card key={schedule.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{schedule.subject}</CardTitle>
                {getStatusBadge(schedule.status)}
              </div>
              <CardDescription>
                Lớp {schedule.class} - Phòng {schedule.room}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  {schedule.dayOfWeek}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="h-4 w-4 mr-2" />
                  {schedule.startTime} - {schedule.endTime}
                </div>
              </div>
              <div className="flex space-x-2 mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => handleScheduleClick(schedule)}
                >
                  Xem chi tiết
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredSchedules.length === 0 && (
        <div className="text-center py-12">
          <Calendar className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            Không có lịch giảng
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm ? 'Không tìm thấy lịch giảng phù hợp.' : 'Chưa có lịch giảng được phân công.'}
          </p>
        </div>
      )}
        </TabsContent>
      </Tabs>

      {/* Detail Modal */}
      <ScheduleDetailModal
        schedule={selectedSchedule}
        isOpen={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
        isAdmin={false}
      />
    </div>
  )
}
