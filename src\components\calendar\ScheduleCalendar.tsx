'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  ChevronLeft,
  ChevronRight,
  Calendar,
  Clock,
  MapPin,
  User,
  Plus,
  Filter
} from 'lucide-react'
import WeekView from './WeekView'
import SessionWeekView from './SessionWeekView'
import { scheduleService } from '@/lib/services/scheduleService'

interface Schedule {
  id: number
  subject: string
  class: string
  teacher: string
  room: string
  startTime: string
  endTime: string
  date: string
  status: 'confirmed' | 'pending' | 'cancelled'
  color: string
}

interface AcademicYear {
  id: number
  name: string
  year: number
  isActive: boolean
}

interface Semester {
  id: number
  name: string
  academicYearId: number
  startDate: string
  endDate: string
  isActive: boolean
}

interface ScheduleCalendarProps {
  onScheduleClick?: (schedule: Schedule) => void
  onDateClick?: (date: string, timeInfo?: { session?: string; timeSlot?: string }) => void
  onCreateSchedule?: (date: string, timeInfo?: { session?: string; timeSlot?: string }) => void
  isAdmin?: boolean
}

export default function ScheduleCalendar({
  onScheduleClick,
  onDateClick,
  onCreateSchedule,
  isAdmin = false
}: ScheduleCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedAcademicYear, setSelectedAcademicYear] = useState<number | null>(null)
  const [selectedSemester, setSelectedSemester] = useState<number | null>(null)
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [academicYears, setAcademicYears] = useState<AcademicYear[]>([])
  const [semesters, setSemesters] = useState<Semester[]>([])
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'session'>('month')
  const [loading, setLoading] = useState(false)

  // Load initial data
  useEffect(() => {
    loadInitialData()
  }, [])

  // Load schedules when semester changes
  useEffect(() => {
    if (selectedSemester) {
      loadSchedules()
    }
  }, [selectedSemester, currentDate])

  const loadInitialData = async () => {
    try {
      setLoading(true)

      // Mock academic years and semesters for now
      // TODO: Replace with real API calls when available
      const mockAcademicYears: AcademicYear[] = [
        { id: 1, name: '2023-2024', year: 2023, isActive: false },
        { id: 2, name: '2024-2025', year: 2024, isActive: true },
        { id: 3, name: '2025-2026', year: 2025, isActive: false }
      ]

      const mockSemesters: Semester[] = [
        { id: 1, name: 'Học kỳ 1', academicYearId: 2, startDate: '2024-09-01', endDate: '2024-12-31', isActive: true },
        { id: 2, name: 'Học kỳ 2', academicYearId: 2, startDate: '2025-01-01', endDate: '2025-05-31', isActive: false },
        { id: 3, name: 'Học kỳ hè', academicYearId: 2, startDate: '2025-06-01', endDate: '2025-08-31', isActive: false }
      ]

      setAcademicYears(mockAcademicYears)
      setSemesters(mockSemesters)

      // Set default selections
      const activeYear = mockAcademicYears.find(y => y.isActive)
      const activeSemester = mockSemesters.find(s => s.isActive)

      if (activeYear) setSelectedAcademicYear(activeYear.id)
      if (activeSemester) setSelectedSemester(activeSemester.id)

    } catch (error) {
      console.error('Error loading initial data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadSchedules = async () => {
    if (!selectedSemester) return

    try {
      setLoading(true)

      // Get date range for current month view
      const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
      const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)

      const startDate = startOfMonth.toISOString().split('T')[0]
      const endDate = endOfMonth.toISOString().split('T')[0]

      // Load schedules from API
      const schedulesData = await scheduleService.getSchedulesForCalendar({
        startDate,
        endDate,
        semesterId: selectedSemester
      })

      // Add colors to schedules for display
      const schedulesWithColors = schedulesData.map((schedule, index) => ({
        ...schedule,
        color: getScheduleColor(index)
      }))

      setSchedules(schedulesWithColors)

    } catch (error) {
      console.error('Error loading schedules:', error)
      // Fallback to empty array on error
      setSchedules([])
    } finally {
      setLoading(false)
    }
  }

  const getScheduleColor = (index: number): string => {
    const colors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-yellow-500',
      'bg-purple-500',
      'bg-red-500',
      'bg-indigo-500',
      'bg-pink-500',
      'bg-teal-500'
    ]
    return colors[index % colors.length]
  }

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }

    return days
  }

  const getDaysInWeek = (date: Date) => {
    const startOfWeek = new Date(date)
    const day = startOfWeek.getDay()
    const diff = startOfWeek.getDate() - day // Adjust to start from Sunday
    startOfWeek.setDate(diff)

    const days = []
    for (let i = 0; i < 7; i++) {
      const currentDay = new Date(startOfWeek)
      currentDay.setDate(startOfWeek.getDate() + i)
      days.push(currentDay)
    }

    return days
  }

  const getSchedulesForDate = (date: Date | null) => {
    if (!date) return []
    const dateString = date.toISOString().split('T')[0]
    return schedules.filter(schedule => schedule.date === dateString)
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const navigateWeek = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setDate(prev.getDate() - 7)
      } else {
        newDate.setDate(prev.getDate() + 7)
      }
      return newDate
    })
  }

  const navigate = (direction: 'prev' | 'next') => {
    if (viewMode === 'month') {
      navigateMonth(direction)
    } else {
      // Both week and session views use week navigation
      navigateWeek(direction)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <Badge variant="default" className="text-xs">Đã xác nhận</Badge>
      case 'pending':
        return <Badge variant="secondary" className="text-xs">Chờ duyệt</Badge>
      case 'cancelled':
        return <Badge variant="destructive" className="text-xs">Đã hủy</Badge>
      default:
        return null
    }
  }

  const filteredSemesters = semesters.filter(s => s.academicYearId === selectedAcademicYear)

  const monthNames = [
    'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
    'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
  ]

  const dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7']

  const getDisplayTitle = () => {
    if (viewMode === 'month') {
      return `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`
    } else {
      // Both week and session views show week range
      const weekDays = getDaysInWeek(currentDate)
      const startDate = weekDays[0]
      const endDate = weekDays[6]

      if (startDate.getMonth() === endDate.getMonth()) {
        return `${startDate.getDate()} - ${endDate.getDate()} ${monthNames[startDate.getMonth()]} ${startDate.getFullYear()}`
      } else {
        return `${startDate.getDate()} ${monthNames[startDate.getMonth()]} - ${endDate.getDate()} ${monthNames[endDate.getMonth()]} ${startDate.getFullYear()}`
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4">
          <Select value={selectedAcademicYear?.toString()} onValueChange={(value) => setSelectedAcademicYear(Number(value))}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Chọn năm học" />
            </SelectTrigger>
            <SelectContent>
              {academicYears.map(year => (
                <SelectItem key={year.id} value={year.id.toString()}>
                  {year.name} {year.isActive && '(Hiện tại)'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedSemester?.toString()} onValueChange={(value) => setSelectedSemester(Number(value))}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Chọn học kỳ" />
            </SelectTrigger>
            <SelectContent>
              {filteredSemesters.map(semester => (
                <SelectItem key={semester.id} value={semester.id.toString()}>
                  {semester.name} {semester.isActive && '(Hiện tại)'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Select value={viewMode} onValueChange={(value: 'month' | 'week' | 'session') => setViewMode(value)}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">Tháng</SelectItem>
              <SelectItem value="week">Tuần</SelectItem>
              <SelectItem value="session">Buổi học</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Calendar Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm" onClick={() => navigate('prev')}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <CardTitle className="text-xl">
                {getDisplayTitle()}
              </CardTitle>
              <Button variant="outline" size="sm" onClick={() => navigate('next')}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            <Button variant="outline" onClick={() => setCurrentDate(new Date())}>
              Hôm nay
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {viewMode === 'month' ? (
            /* Month View - Calendar Grid */
            <div className="grid grid-cols-7 gap-1">
              {/* Day headers */}
              {dayNames.map(day => (
                <div key={day} className="p-2 text-center text-sm font-medium text-gray-500 border-b">
                  {day}
                </div>
              ))}

              {/* Calendar days */}
              {getDaysInMonth(currentDate).map((date, index) => {
                const daySchedules = getSchedulesForDate(date)
                const isToday = date && date.toDateString() === new Date().toDateString()

                return (
                  <div
                    key={index}
                    className={`min-h-[120px] p-1 border border-gray-200 ${
                      date ? 'bg-white hover:bg-gray-50 cursor-pointer' : 'bg-gray-50'
                    } ${isToday ? 'bg-blue-50 border-blue-200' : ''}`}
                    onClick={() => {
                      if (date) {
                        onDateClick?.(date.toISOString().split('T')[0])
                      }
                    }}
                  >
                    {date && (
                      <>
                        <div className="flex items-center justify-between mb-1">
                          <span className={`text-sm ${isToday ? 'font-bold text-blue-600' : ''}`}>
                            {date.getDate()}
                          </span>
                          {isAdmin && (
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                              onClick={(e) => {
                                e.stopPropagation()
                                onCreateSchedule?.(date.toISOString().split('T')[0])
                              }}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          )}
                        </div>

                        <div className="space-y-1">
                          {daySchedules.slice(0, 3).map(schedule => (
                            <div
                              key={schedule.id}
                              className={`text-xs p-1 rounded text-white cursor-pointer ${schedule.color} hover:opacity-80`}
                              onClick={(e) => {
                                e.stopPropagation()
                                onScheduleClick?.(schedule)
                              }}
                            >
                              <div className="font-medium truncate">{schedule.subject}</div>
                              <div className="truncate">{schedule.startTime} - {schedule.class}</div>
                            </div>
                          ))}
                          {daySchedules.length > 3 && (
                            <div className="text-xs text-gray-500 text-center">
                              +{daySchedules.length - 3} khác
                            </div>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                )
              })}
            </div>
          ) : null}
        </CardContent>
      </Card>

      {/* Week View */}
      {viewMode === 'week' && (
        <WeekView
          currentDate={currentDate}
          schedules={schedules}
          onScheduleClick={onScheduleClick}
          onDateClick={onDateClick}
          onCreateSchedule={onCreateSchedule}
          isAdmin={isAdmin}
        />
      )}

      {/* Session Week View */}
      {viewMode === 'session' && (
        <SessionWeekView
          currentDate={currentDate}
          schedules={schedules}
          onScheduleClick={onScheduleClick}
          onDateClick={onDateClick}
          onCreateSchedule={onCreateSchedule}
          isAdmin={isAdmin}
        />
      )}
    </div>
  )
}
