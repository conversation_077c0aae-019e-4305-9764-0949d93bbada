# 📅 Schedule API Update Summary

## 🎯 Objective
Update schedule service to match the official Schedule Calendar API documentation (`SCHEDULE_CALENDAR_API_DOCUMENTATION.md`).

## ✅ Changes Made

### 1. **Updated API Configuration**
**File:** `src/lib/config/api.ts`

Added calendar endpoint:
```typescript
SCHEDULES: {
  BASE: '/api/schedules',
  CALENDAR: '/api/schedules/calendar', // NEW
  TEACHER: '/api/schedules/teacher',
  IMPORT: '/api/schedules/import',
  EXPORT: '/api/schedules/export'
}
```

### 2. **New Interfaces for API Response**
**File:** `src/lib/services/scheduleService.ts`

#### CalendarEvent Interface (API Response):
```typescript
export interface CalendarEvent {
  id: number
  title: string
  description: string
  start: string // ISO datetime
  end: string // ISO datetime
  allDay: boolean
  backgroundColor: string
  borderColor: string
  textColor: string
  extendedProps: {
    scheduleId: number
    teacherId: number
    teacherName: string
    subjectCode: string
    subjectName: string
    classCode: string
    className: string
    roomName: string
    campusName: string
    sessionName: string
    formType: string
    practiceGroup?: string
    credits: number
    coefficient: number
    note?: string
  }
}
```

#### API Response Interface:
```typescript
export interface ScheduleCalendarResponse {
  success: boolean
  message: string
  data: CalendarEvent[]
}
```

### 3. **Enhanced Schedule Interface**
Added new fields from API:
```typescript
export interface Schedule {
  // ... existing fields
  subjectCode?: string
  classCode?: string
  campusName?: string
  sessionName?: string
  practiceGroup?: string
  credits?: number
  coefficient?: number
}
```

### 4. **New Data Conversion Function**
```typescript
function convertCalendarEventToSchedule(event: CalendarEvent): Schedule {
  const startDate = new Date(event.start)
  const endDate = new Date(event.end)
  const scheduleDate = event.start.split('T')[0]
  
  return {
    id: event.id,
    subject: event.extendedProps.subjectName,
    class: event.extendedProps.className,
    teacher: event.extendedProps.teacherName,
    room: event.extendedProps.roomName,
    dayOfWeek: getDayOfWeekFromDate(startDate),
    startTime: startDate.toTimeString().slice(0, 5),
    endTime: endDate.toTimeString().slice(0, 5),
    scheduleDate: scheduleDate,
    // ... additional fields
  }
}
```

### 5. **Updated Service Methods**

#### getAllSchedules():
- Now uses `/api/schedules/calendar` endpoint
- Requires date range (defaults to current month)
- Supports new parameters: `departmentId`

#### getSchedulesByTeacher():
- Uses calendar API with `teacherId` filter
- Returns properly formatted Schedule objects

#### getSchedulesForCalendar():
- Main method for calendar views
- Supports all API parameters: `semesterId`, `teacherId`, `classId`, `departmentId`

#### New Method - getCalendarEvents():
- Returns raw CalendarEvent objects
- For calendar libraries that need original API format

### 6. **API Parameters Support**

| Parameter | Type | Description | Usage |
|-----------|------|-------------|-------|
| `startDate` | String | Start date (ISO format) | Required |
| `endDate` | String | End date (ISO format) | Required |
| `semesterId` | Long | Filter by semester | Optional |
| `teacherId` | Long | Filter by teacher | Optional |
| `classId` | Long | Filter by class | Optional |
| `departmentId` | Long | Filter by department | Optional |

### 7. **Updated Calendar Component**
**File:** `src/components/calendar/ScheduleCalendar.tsx`

- Uses new API format
- Preserves colors from API response
- Maintains backward compatibility

## 🔄 API Usage Examples

### **Get All Schedules for Current Month:**
```typescript
const schedules = await scheduleService.getAllSchedules({
  semesterId: 1
})
```

### **Get Teacher Schedules:**
```typescript
const teacherSchedules = await scheduleService.getSchedulesByTeacher(123, 1)
```

### **Get Calendar Events for Date Range:**
```typescript
const events = await scheduleService.getSchedulesForCalendar({
  startDate: '2025-05-31',
  endDate: '2025-06-29',
  semesterId: 1,
  teacherId: 123
})
```

### **Get Raw Calendar Events:**
```typescript
const rawEvents = await scheduleService.getCalendarEvents({
  startDate: '2025-05-31',
  endDate: '2025-06-29',
  semesterId: 1
})
```

## 🎨 Color Mapping

API automatically assigns colors based on form type:

| Form Type | Color | Hex Code |
|-----------|-------|----------|
| Lý thuyết (LT) | 🔵 Blue | `#3788d8` |
| Thực hành (TH) | 🟢 Green | `#28a745` |
| Bài tập (BT) | 🟡 Yellow | `#ffc107` |
| Thực nghiệm (TN) | 🔴 Red | `#dc3545` |
| Đồ án (DA) | 🟣 Purple | `#6f42c1` |
| Khác | ⚫ Gray | `#6c757d` |

## 🔒 Security & Permissions

API automatically filters data based on user role:
- **ADMIN**: See all schedules
- **TRUONG_KHOA**: See department schedules only
- **GIANG_VIEN**: See personal schedules only

## ⚡ Performance Features

- **Date Range Validation**: Max 3 months (90 days)
- **Automatic Filtering**: Role-based data access
- **Rich Metadata**: Full schedule information in `extendedProps`
- **Color Coding**: Automatic color assignment

## 🔧 Integration Benefits

1. **Standardized Format**: Matches official API documentation
2. **Rich Data**: More detailed schedule information
3. **Better Performance**: Optimized for calendar views
4. **Role-Based Security**: Automatic data filtering
5. **Color Coding**: Visual distinction by lesson type
6. **Flexible Filtering**: Multiple filter options

## 🚀 Backward Compatibility

- Existing Schedule interface maintained
- Calendar components work without changes
- Form integration preserved
- Error handling improved

## 📊 Data Flow

```
API Response (CalendarEvent) 
    ↓
convertCalendarEventToSchedule()
    ↓
Schedule Object (Frontend)
    ↓
UI Components
```

---

**Status:** ✅ **COMPLETED**  
**API Version:** 1.0.0  
**Compatibility:** Full backward compatibility maintained  
**Documentation:** Matches `SCHEDULE_CALENDAR_API_DOCUMENTATION.md`
