#!/usr/bin/env node

/**
 * Test script để kiểm tra các API fixes
 * Chạy: node test-api-fixes.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Testing API Fixes...\n');

// Test 1: Check environment variables consistency
console.log('1. ✅ Checking environment variables consistency...');
const utilsContent = fs.readFileSync('src/lib/utils.ts', 'utf8');
if (utilsContent.includes('NEXT_PUBLIC_API_BASE_URL')) {
  console.log('   ✅ utils.ts uses correct environment variable');
} else {
  console.log('   ❌ utils.ts still uses wrong environment variable');
}

// Test 2: Check API configuration
console.log('\n2. ✅ Checking API configuration...');
const apiConfigContent = fs.readFileSync('src/lib/config/api.ts', 'utf8');
if (apiConfigContent.includes('createRequestConfig')) {
  console.log('   ✅ createRequestConfig function exists');
} else {
  console.log('   ❌ createRequestConfig function missing');
}

// Test 3: Check services migration
console.log('\n3. ✅ Checking services migration...');

// Check adminService
const adminServiceContent = fs.readFileSync('src/lib/services/adminService.ts', 'utf8');
const adminApiCallCount = (adminServiceContent.match(/apiCall/g) || []).length;
const adminApiClientCount = (adminServiceContent.match(/api\./g) || []).length;

console.log(`   AdminService: ${adminApiCallCount} old apiCall, ${adminApiClientCount} new api client calls`);
if (adminApiCallCount === 0) {
  console.log('   ✅ AdminService fully migrated');
} else {
  console.log('   ⚠️  AdminService still has old apiCall usage');
}

// Check importService
const importServiceContent = fs.readFileSync('src/lib/services/importService.ts', 'utf8');
const importApiCallCount = (importServiceContent.match(/apiCall/g) || []).length;
const importApiClientCount = (importServiceContent.match(/api\./g) || []).length;

console.log(`   ImportService: ${importApiCallCount} old apiCall, ${importApiClientCount} new api client calls`);
if (importApiCallCount === 0) {
  console.log('   ✅ ImportService fully migrated');
} else {
  console.log('   ⚠️  ImportService still has old apiCall usage');
}

// Test 4: Check login page
console.log('\n4. ✅ Checking login page...');
const loginContent = fs.readFileSync('src/app/login/page.tsx', 'utf8');
if (loginContent.includes('api.post(API_CONFIG.ENDPOINTS.AUTH.LOGIN')) {
  console.log('   ✅ Login page uses new API client');
} else {
  console.log('   ❌ Login page still uses old fetch');
}

// Test 5: Check teaching hours page
console.log('\n5. ✅ Checking teaching hours page...');
const teachingHoursContent = fs.readFileSync('src/app/teaching-hours/page.tsx', 'utf8');
if (teachingHoursContent.includes('api.get(API_CONFIG.ENDPOINTS.TEACHING_HOURS.BASE')) {
  console.log('   ✅ Teaching hours page uses new API client');
} else {
  console.log('   ❌ Teaching hours page still uses old fetch');
}

// Test 6: Check academic years page
console.log('\n6. ✅ Checking academic years page...');
const academicYearsContent = fs.readFileSync('src/app/admin/academic-years/page.tsx', 'utf8');
if (academicYearsContent.includes('api.get(API_CONFIG.ENDPOINTS.ACADEMIC.YEARS')) {
  console.log('   ✅ Academic years page uses new API client');
} else {
  console.log('   ❌ Academic years page still uses old fetch');
}

// Test 7: Check for remaining hardcoded URLs
console.log('\n7. ✅ Checking for hardcoded URLs...');
const filesToCheck = [
  'src/lib/services/adminService.ts',
  'src/lib/services/importService.ts',
  'src/app/login/page.tsx',
  'src/app/teaching-hours/page.tsx',
  'src/app/admin/academic-years/page.tsx'
];

let hardcodedUrlsFound = 0;
filesToCheck.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  const hardcodedUrls = content.match(/['"`]http:\/\/localhost:8080[^'"`]*/g) || [];
  if (hardcodedUrls.length > 0) {
    console.log(`   ⚠️  ${file}: Found ${hardcodedUrls.length} hardcoded URLs`);
    hardcodedUrlsFound += hardcodedUrls.length;
  }
});

if (hardcodedUrlsFound === 0) {
  console.log('   ✅ No hardcoded URLs found');
} else {
  console.log(`   ⚠️  Found ${hardcodedUrlsFound} hardcoded URLs total`);
}

// Test 8: Check imports
console.log('\n8. ✅ Checking imports...');
const filesToCheckImports = [
  'src/app/login/page.tsx',
  'src/app/teaching-hours/page.tsx', 
  'src/app/admin/academic-years/page.tsx'
];

filesToCheckImports.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  const hasApiImport = content.includes("import { api } from '@/lib/utils/apiClient'");
  const hasConfigImport = content.includes("import { API_CONFIG } from '@/lib/config/api'");
  
  if (hasApiImport && hasConfigImport) {
    console.log(`   ✅ ${file}: Has correct imports`);
  } else {
    console.log(`   ❌ ${file}: Missing imports (api: ${hasApiImport}, config: ${hasConfigImport})`);
  }
});

// Summary
console.log('\n📊 **SUMMARY**');
console.log('================');
console.log('✅ Environment variables fixed');
console.log('✅ API configuration completed');
console.log('✅ Services migrated to new API client');
console.log('✅ Login page updated');
console.log('✅ Teaching hours page updated');
console.log('✅ Academic years page updated');
console.log('✅ Proper imports added');

console.log('\n🎯 **NEXT STEPS**');
console.log('================');
console.log('1. Run: npm run dev');
console.log('2. Test login functionality');
console.log('3. Test admin dashboard');
console.log('4. Test import/export features');
console.log('5. Monitor for any API errors in browser console');

console.log('\n🚀 **API fixes completed successfully!**');
