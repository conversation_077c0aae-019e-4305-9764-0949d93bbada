# Hướng dẫn Import & Đồng bộ dữ liệu

Hệ thống quản lý lịch giảng hỗ trợ import và đồng bộ dữ liệu từ các phần mềm khác để có sẵn thông tin cơ bản như mô<PERSON> họ<PERSON>, l<PERSON><PERSON>, g<PERSON><PERSON><PERSON> viên, <PERSON>h<PERSON><PERSON> họ<PERSON>, nă<PERSON> học và học kỳ.

## 🎯 Mục đích

Thay vì phải nhập thủ công từng thông tin, hệ thống cho phép:
- **Đồng bộ tự động** từ hệ thống nguồn (ERP, LMS, etc.)
- **Import từ file** Excel/CSV với template có sẵn
- **Quản lý trạng thái** đồng bộ của từng loại dữ liệu

## 📊 Các loại dữ liệu hỗ trợ

### 1. **<PERSON><PERSON><PERSON> (Subjects)**
- <PERSON><PERSON> mô<PERSON> họ<PERSON>, tê<PERSON> môn học
- <PERSON><PERSON> tín chỉ, lo<PERSON><PERSON> m<PERSON> (bắt buộc/tự chọn)
- Template: `ma_mon_hoc,ten_mon_hoc,so_tin_chi,loai_mon`

### 2. **Lớp học (Classes)**
- Mã lớp, tên lớp
- Khoa, khóa học, sĩ số
- Template: `ma_lop,ten_lop,khoa,khoa_hoc,si_so`

### 3. **Giảng viên (Teachers)**
- Mã cán bộ, tên, email, số điện thoại
- Khoa, chức vụ
- Template: `ma_can_bo,ten_can_bo,email,so_dien_thoai,khoa,chuc_vu`

### 4. **Phòng học (Rooms)**
- Mã phòng, tên phòng
- Loại phòng, sức chứa, cơ sở
- Template: `ma_phong,ten_phong,loai_phong,suc_chua,co_so`

### 5. **Năm học/Học kỳ (Academic Years)**
- Năm học, học kỳ
- Ngày bắt đầu, kết thúc, trạng thái
- Template: `nam_hoc,hoc_ky,ngay_bat_dau,ngay_ket_thuc,trang_thai`

## 🚀 Cách sử dụng

### Truy cập trang Import
1. Đăng nhập vào hệ thống
2. Vào **Dashboard** → **Import & Đồng bộ**
3. Hoặc truy cập trực tiếp: `http://localhost:3000/import`

### Đồng bộ tự động
1. Click nút **"Đồng bộ"** cho từng loại dữ liệu
2. Hoặc click **"Đồng bộ tất cả"** để sync toàn bộ
3. Theo dõi trạng thái và số lượng bản ghi

### Import từ file
1. Click **"Template"** để tải file mẫu
2. Điền dữ liệu vào file Excel/CSV
3. Click **"Import File"** và chọn file đã chuẩn bị
4. Hệ thống sẽ xử lý và báo kết quả

## 📋 Template Files

### Môn học (subjects.csv)
```csv
ma_mon_hoc,ten_mon_hoc,so_tin_chi,loai_mon
IT001,Lập trình Java,3,Bắt buộc
IT002,Cơ sở dữ liệu,3,Bắt buộc
IT003,Mạng máy tính,3,Tự chọn
```

### Lớp học (classes.csv)
```csv
ma_lop,ten_lop,khoa,khoa_hoc,si_so
CNTT01,Công nghệ thông tin 01,CNTT,2023,35
CNTT02,Công nghệ thông tin 02,CNTT,2023,32
KTPM01,Kỹ thuật phần mềm 01,CNTT,2023,28
```

### Giảng viên (teachers.csv)
```csv
ma_can_bo,ten_can_bo,email,so_dien_thoai,khoa,chuc_vu
GV001,Nguyễn Văn A,<EMAIL>,0123456789,CNTT,Giảng viên
GV002,Trần Thị B,<EMAIL>,0987654321,CNTT,Phó giáo sư
```

### Phòng học (rooms.csv)
```csv
ma_phong,ten_phong,loai_phong,suc_chua,co_so
A101,Phòng A101,Lý thuyết,50,Cơ sở 1
B201,Phòng B201,Thực hành,30,Cơ sở 1
C301,Phòng C301,Hội thảo,100,Cơ sở 2
```

### Năm học (academic_years.csv)
```csv
nam_hoc,hoc_ky,ngay_bat_dau,ngay_ket_thuc,trang_thai
2023-2024,1,2023-09-01,2024-01-15,Đã kết thúc
2023-2024,2,2024-01-16,2024-05-30,Đang diễn ra
2024-2025,1,2024-09-01,2025-01-15,Sắp diễn ra
```

## 🔧 API Endpoints

### Backend APIs
- `POST /api/import/sync/{type}` - Đồng bộ từ hệ thống nguồn
- `POST /api/import/upload` - Import từ file upload
- `GET /api/import/status` - Lấy trạng thái import
- `DELETE /api/import/clear/{type}` - Xóa dữ liệu
- `GET /api/import/test-connection` - Kiểm tra kết nối

### Supported Types
- `subjects` - Môn học
- `classes` - Lớp học  
- `teachers` - Giảng viên
- `rooms` - Phòng học
- `academic_years` - Năm học/Học kỳ

## 📊 Giao diện Import

### Tổng quan dữ liệu
- Số lượng loại dữ liệu đã đồng bộ
- Số lượng loại dữ liệu chờ đồng bộ
- Tổng số bản ghi trong hệ thống

### Quản lý từng loại dữ liệu
- **Icon và màu sắc** phân biệt từng loại
- **Trạng thái** hiện tại (thành công/chờ/lỗi)
- **Thời gian** đồng bộ gần nhất
- **Số lượng** bản ghi hiện có

### Thao tác nhanh
- **Template** - Tải file mẫu
- **Import File** - Upload file dữ liệu
- **Đồng bộ** - Sync từ hệ thống nguồn

## ⚙️ Cấu hình

### File formats hỗ trợ
- **CSV** với encoding UTF-8
- **Excel** (.xlsx, .xls)
- **Delimiter**: Dấu phẩy (,)

### Thứ tự import khuyến nghị
1. **Năm học/Học kỳ** - Thiết lập khung thời gian
2. **Môn học** - Danh sách các môn
3. **Giảng viên** - Đội ngũ giảng dạy
4. **Phòng học** - Cơ sở vật chất
5. **Lớp học** - Đối tượng học tập

### Validation rules
- **Mã không được trùng** trong cùng loại
- **Email phải đúng định dạng** cho giảng viên
- **Ngày tháng đúng format** YYYY-MM-DD
- **Số lượng phải là số nguyên dương**

## 🔄 Tích hợp hệ thống nguồn

### Cấu hình kết nối
```java
// Trong ImportServiceImpl.java
@Override
public boolean testSourceConnection() {
    // Implement connection to your source system
    // Example: ERP, LMS, Student Information System
    return sourceSystemClient.testConnection();
}
```

### Đồng bộ tự động
```java
@Override
public Map<String, Object> syncDataFromSource(String type) {
    // Implement sync logic for each data type
    switch (type) {
        case "subjects":
            return syncSubjectsFromERP();
        case "teachers":
            return syncTeachersFromHR();
        // ... other cases
    }
}
```

## 🚨 Xử lý lỗi

### Lỗi thường gặp
1. **File format không đúng** - Kiểm tra extension và encoding
2. **Dữ liệu trùng lặp** - Xem lại mã định danh
3. **Thiếu cột bắt buộc** - So sánh với template
4. **Kết nối thất bại** - Kiểm tra network và credentials

### Logging và monitoring
- Tất cả thao tác import được log
- Trạng thái đồng bộ được lưu trữ
- Thông báo lỗi chi tiết cho user

## 📈 Performance

### Tối ưu hóa
- **Batch processing** cho file lớn
- **Transaction management** đảm bảo data integrity
- **Async processing** cho đồng bộ tự động
- **Caching** cho lookup data

### Giới hạn
- File upload tối đa: **10MB**
- Số bản ghi mỗi lần: **1000 records**
- Timeout đồng bộ: **30 seconds**

## 🔐 Bảo mật

### Phân quyền
- Chỉ **Admin** và **Trưởng khoa** có quyền import
- Log tất cả thao tác import
- Validate dữ liệu trước khi lưu

### Data protection
- Backup trước khi import
- Rollback mechanism khi có lỗi
- Encrypt sensitive data

## 📞 Hỗ trợ

Nếu gặp vấn đề với tính năng import:
1. Kiểm tra format file theo template
2. Xem log lỗi trong console
3. Test kết nối hệ thống nguồn
4. Liên hệ admin để được hỗ trợ

---

**Lưu ý**: Tính năng này giúp tiết kiệm thời gian setup ban đầu và đảm bảo tính nhất quán của dữ liệu trong hệ thống quản lý lịch giảng.
