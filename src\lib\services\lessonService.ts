import { api } from '@/lib/utils/apiClient'
import { API_CONFIG } from '@/lib/config/api'
import { withCache, CACHE_KEYS } from '@/lib/utils/apiCache'

// Lesson interface based on API documentation
export interface Lesson {
  idBaiHoc?: number
  idMonHoc: number
  maBaiHoc: string
  tenBaiHoc: string
  sttBaiHoc: number
  soTietLt: number
  soTietTh: number
  soTietTu: number
  mucTieu?: string
  noiDung?: string
  phuongPhapDay?: string
  taiLieuThamKhao?: string
  baiTap?: string
  danhGia?: string
  ghiChu?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

// Request interface for creating/updating lessons
export interface LessonRequest {
  idMonHoc: number
  maBaiHoc: string
  tenBaiHoc: string
  sttBaiHoc?: number
  soTietLt: number
  soTietTh: number
  soTietTu: number
  mucTieu?: string
  noiDung?: string
  phuongPhapDay?: string
  taiLieuTham<PERSON>hao?: string
  baiTap?: string
  danhGia?: string
  ghiChu?: string
  trangThai?: boolean
}

// Paginated response interface
export interface PaginatedLessonResponse {
  content: Lesson[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
  empty: boolean
}

// Lesson statistics interface
export interface LessonStatistics {
  totalLessons: number
  totalTheoryHours: number
  totalPracticeHours: number
  totalSelfStudyHours: number
  averageHoursPerLesson: number
}

// Lesson service for frontend form usage
export interface LessonForForm {
  id: string
  name: string
  types: ('LT' | 'TH')[]
  defaultType: 'LT' | 'TH'
  theoryHours: number
  practiceHours: number
  selfStudyHours: number
  order: number
}

class LessonService {
  // Get lessons with pagination
  async getLessons(
    page = 0, 
    size = 10, 
    sortBy = 'sttBaiHoc', 
    sortDir = 'asc'
  ): Promise<PaginatedLessonResponse> {
    const params = { page, size, sortBy, sortDir }
    const response = await api.get(API_CONFIG.ENDPOINTS.LESSONS.BASE, params)
    return response.data
  }

  // Get lessons by subject ID with pagination
  async getLessonsBySubject(
    idMonHoc: number, 
    page = 0, 
    size = 10
  ): Promise<PaginatedLessonResponse> {
    const params = { page, size }
    const response = await api.get(`${API_CONFIG.ENDPOINTS.LESSONS.BY_SUBJECT}/${idMonHoc}`, params)
    return response.data
  }

  // Get all lessons by subject (no pagination) - for form usage
  async getAllLessonsBySubject(idMonHoc: number): Promise<Lesson[]> {
    const cacheKey = `${CACHE_KEYS.LESSONS_BY_SUBJECT}_${idMonHoc}`
    
    return withCache(
      cacheKey,
      async () => {
        try {
          const response = await api.get(`${API_CONFIG.ENDPOINTS.LESSONS.BY_SUBJECT}/${idMonHoc}/all`)
          return response.data || []
        } catch (error) {
          console.error('Error fetching lessons by subject:', error)
          // Return empty array if API fails
          return []
        }
      },
      5 * 60 * 1000 // Cache for 5 minutes
    )
  }

  // Convert backend lessons to form-friendly format
  convertLessonsForForm(lessons: Lesson[]): LessonForForm[] {
    return lessons
      .filter(lesson => lesson.trangThai) // Only active lessons
      .sort((a, b) => a.sttBaiHoc - b.sttBaiHoc) // Sort by order
      .map(lesson => {
        const types: ('LT' | 'TH')[] = []
        let defaultType: 'LT' | 'TH' = 'LT'

        // Determine available types based on hours
        if (lesson.soTietLt > 0) {
          types.push('LT')
          defaultType = 'LT'
        }
        if (lesson.soTietTh > 0) {
          types.push('TH')
          // If only practice hours, set as default
          if (lesson.soTietLt === 0) {
            defaultType = 'TH'
          }
        }

        // If no types available, default to LT
        if (types.length === 0) {
          types.push('LT')
        }

        return {
          id: lesson.idBaiHoc?.toString() || lesson.maBaiHoc,
          name: lesson.tenBaiHoc,
          types,
          defaultType,
          theoryHours: lesson.soTietLt,
          practiceHours: lesson.soTietTh,
          selfStudyHours: lesson.soTietTu,
          order: lesson.sttBaiHoc
        }
      })
  }

  // Get lessons for form by subject ID
  async getLessonsForForm(idMonHoc: number): Promise<LessonForForm[]> {
    const lessons = await this.getAllLessonsBySubject(idMonHoc)
    return this.convertLessonsForForm(lessons)
  }

  // Search lessons
  async searchLessons(keyword: string, page = 0, size = 10): Promise<PaginatedLessonResponse> {
    const params = { keyword, page, size }
    const response = await api.get(API_CONFIG.ENDPOINTS.LESSONS.SEARCH, params)
    return response.data
  }

  // Get lesson by ID
  async getLessonById(id: number): Promise<Lesson> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.LESSONS.BASE}/${id}`)
    return response.data
  }

  // Create new lesson
  async createLesson(lesson: LessonRequest): Promise<Lesson> {
    const response = await api.post(API_CONFIG.ENDPOINTS.LESSONS.BASE, lesson)
    return response.data
  }

  // Update lesson
  async updateLesson(id: number, lesson: Partial<LessonRequest>): Promise<Lesson> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.LESSONS.BASE}/${id}`, lesson)
    return response.data
  }

  // Delete lesson
  async deleteLesson(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.LESSONS.BASE}/${id}`)
  }

  // Toggle lesson status
  async toggleLessonStatus(id: number): Promise<Lesson> {
    const response = await api.patch(`${API_CONFIG.ENDPOINTS.LESSONS.BASE}/${id}/toggle-status`)
    return response.data
  }

  // Get lesson statistics by subject
  async getLessonStatistics(idMonHoc: number): Promise<LessonStatistics> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.LESSONS.STATISTICS}/mon-hoc/${idMonHoc}`)
    return response.data
  }

  // Generate next lesson order number
  async generateNextOrder(idMonHoc: number): Promise<number> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.LESSONS.GENERATE_STT}/${idMonHoc}`)
    return response.data
  }
}

export const lessonService = new LessonService()
export default lessonService
