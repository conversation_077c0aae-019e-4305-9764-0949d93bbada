# 🐛 Schedule Calendar Debug Guide

## 🎯 Problem
Calendar không hiển thị dữ liệu mặc dù API có trả về data.

## 🔍 Debug Steps

### **Step 1: Access Debug Tool**
Navigate to: `/debug/schedule-api`

### **Step 2: Check Console Logs**
Open Browser DevTools (F12) → Console tab

### **Step 3: Test API Calls**
1. Set appropriate date range (e.g., current month)
2. Set semester ID (e.g., 1)
3. Click "Test Converted Schedules API"
4. Check console for detailed logs

### **Step 4: Analyze Debug Logs**

Look for these log messages in sequence:

#### **🚀 API Call Logs:**
```
🚀 Calling calendar API with params: {startDate: "2024-12-01", endDate: "2024-12-31", semesterId: 1}
📡 API endpoint: /api/schedules/calendar
```

#### **📥 API Response Logs:**
```
📥 Raw API response: {data: {...}}
📊 Parsed API response: {success: true, message: "...", data: [...]}
```

#### **🔄 Data Conversion Logs:**
```
✅ API returned data, converting calendar events...
🔄 Converting calendar event: {id: 1, title: "...", ...}
📅 Parsed dates: {original: "2025-06-02T07:00:00", startDate: Date, ...}
✅ Converted schedule: {id: 1, subject: "...", date: "2025-06-02", ...}
```

#### **📅 Calendar Component Logs:**
```
📅 Loading schedules for date range: 2024-12-01 to 2024-12-31, semester: 1
📊 Raw schedules data from API: [...]
🎨 Schedules with colors: [...]
🔍 Looking for schedules on date: 2024-12-16
📋 Available schedules: [...]
✅ Found X schedules for 2024-12-16: [...]
```

## 🔧 Common Issues & Solutions

### **Issue 1: No API Call**
**Symptoms:**
- No "🚀 Calling calendar API" logs
- Calendar shows loading but no data

**Possible Causes:**
- `selectedSemester` is null/undefined
- Component not triggering `loadSchedules()`

**Solution:**
```typescript
// Check if semester is selected
console.log('Selected semester:', selectedSemester)

// Force load schedules
useEffect(() => {
  if (selectedSemester) {
    loadSchedules()
  }
}, [selectedSemester, currentDate])
```

### **Issue 2: API Returns Empty Data**
**Symptoms:**
- API call successful but `data: []`
- "⚠️ API response indicates no data" logs

**Possible Causes:**
- No schedules in date range
- Wrong semester ID
- Backend filtering issues

**Solution:**
- Check date range matches actual schedule dates
- Verify semester ID exists in database
- Test with broader date range

### **Issue 3: Data Conversion Errors**
**Symptoms:**
- "❌ Error converting calendar event" logs
- Partial schedule data

**Possible Causes:**
- API response format mismatch
- Missing required fields in `extendedProps`
- Timezone conversion issues

**Solution:**
```typescript
// Check API response format
console.log('Raw calendar event:', event)
console.log('Extended props:', event.extendedProps)

// Verify required fields exist
if (!event.extendedProps?.subjectName) {
  console.error('Missing subjectName in extendedProps')
}
```

### **Issue 4: Date Matching Issues**
**Symptoms:**
- Schedules loaded but not displayed on calendar
- "Found 0 schedules for date" logs

**Possible Causes:**
- Date format mismatch
- Timezone conversion errors
- Wrong date field used for filtering

**Solution:**
```typescript
// Check date formats
console.log('Looking for date:', dateString)
console.log('Schedule dates:', schedules.map(s => s.date))
console.log('Schedule scheduleDates:', schedules.map(s => s.scheduleDate))

// Use both date fields for filtering
const matchingSchedules = schedules.filter(schedule => 
  schedule.date === dateString || schedule.scheduleDate === dateString
)
```

### **Issue 5: Missing Colors**
**Symptoms:**
- Schedules display but no background colors
- White/transparent schedule blocks

**Possible Causes:**
- Color field not set properly
- CSS class not applied
- Color format mismatch

**Solution:**
```typescript
// Check color values
console.log('Schedule colors:', schedules.map(s => ({ id: s.id, color: s.color })))

// Ensure color fallback
const color = schedule.color || getScheduleColor(schedule.id)
```

## 🧪 Test Scenarios

### **Test 1: Basic API Call**
```typescript
// Parameters
{
  startDate: "2024-12-01",
  endDate: "2024-12-31", 
  semesterId: 1
}

// Expected: API call successful, data returned
```

### **Test 2: Date Range Validation**
```typescript
// Test with current month
const now = new Date()
const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)

// Expected: Schedules for current month
```

### **Test 3: Teacher Filter**
```typescript
// Parameters
{
  startDate: "2024-12-01",
  endDate: "2024-12-31",
  semesterId: 1,
  teacherId: 123
}

// Expected: Only schedules for teacher 123
```

## 📊 Debug Checklist

- [ ] API endpoint correct (`/api/schedules/calendar`)
- [ ] Parameters sent correctly
- [ ] API returns success response
- [ ] Calendar events have required fields
- [ ] Data conversion successful
- [ ] Schedules have correct date format
- [ ] Calendar component receives schedules
- [ ] Date filtering works correctly
- [ ] Colors applied properly
- [ ] No JavaScript errors in console

## 🔧 Quick Fixes

### **Force Reload Schedules:**
```typescript
// In browser console
window.location.reload()
```

### **Check API Response:**
```typescript
// In browser console
fetch('/api/schedules/calendar?startDate=2024-12-01&endDate=2024-12-31&semesterId=1')
  .then(r => r.json())
  .then(console.log)
```

### **Test Date Conversion:**
```typescript
// In browser console
const testDate = new Date('2024-12-16T07:00:00')
console.log('Date string:', testDate.toISOString().split('T')[0])
console.log('Time string:', testDate.toTimeString().slice(0, 5))
```

## 📞 Support

If issues persist after following this guide:

1. **Capture Debug Logs**: Copy all console logs
2. **Document Steps**: What you tried and results
3. **API Response**: Include raw API response
4. **Browser Info**: Browser version and any errors

---

**Status:** 🔧 **DEBUG TOOL READY**  
**Access:** `/debug/schedule-api`  
**Console:** F12 → Console tab
