# 🔧 API Configuration Guide

## 📋 Tổng Quan

Hệ thống đã được cấu hình với API client thống nhất và base URL có thể cấu hình qua environment variables.

## 🏗️ Cấu Trúc API

### **1. API Configuration (`src/lib/config/api.ts`)**

```typescript
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080',
  ENDPOINTS: {
    AUTH: { LOGIN: '/api/auth/login', ... },
    ADMIN: { DASHBOARD: '/api/admin/dashboard', ... },
    MASTER_DATA: { DEPARTMENTS: '/api/master-data/departments', ... },
    // ... more endpoints
  },
  TIMEOUT: 30000,
  DEFAULT_HEADERS: { 'Content-Type': 'application/json' }
}
```

### **2. API Client (`src/lib/utils/apiClient.ts`)**

```typescript
// Singleton API client với các method:
export const api = {
  get: <T>(endpoint: string, params?: Record<string, any>) => Promise<T>,
  post: <T>(endpoint: string, body?: any) => Promise<T>,
  put: <T>(endpoint: string, body?: any) => Promise<T>,
  patch: <T>(endpoint: string, body?: any) => Promise<T>,
  delete: <T>(endpoint: string) => Promise<T>,
  upload: <T>(endpoint: string, file: File) => Promise<T>,
  download: (endpoint: string, filename?: string) => Promise<void>
}
```

## 🔧 Environment Variables

### **Required Variables**
```bash
# .env.local
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
```

### **Optional Variables**
```bash
# App Configuration
NEXT_PUBLIC_APP_NAME="Hệ thống quản lý lịch giảng"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# File Upload
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=.xlsx,.xls,.csv

# Pagination
NEXT_PUBLIC_DEFAULT_PAGE_SIZE=20
NEXT_PUBLIC_MAX_PAGE_SIZE=100
```

## 📡 API Endpoints

### **Authentication**
- `POST /api/auth/login` - Đăng nhập
- `POST /api/auth/logout` - Đăng xuất
- `GET /api/auth/profile` - Lấy thông tin profile

### **Admin**
- `GET /api/admin/dashboard` - Dashboard data
- `POST /api/admin/backup` - Tạo backup
- `POST /api/admin/initialize` - Khởi tạo dữ liệu

### **Master Data**
- `GET /api/master-data/departments` - Danh sách khoa
- `GET /api/master-data/subjects` - Danh sách môn học
- `GET /api/master-data/classes` - Danh sách lớp học
- `GET /api/master-data/rooms` - Danh sách phòng học
- `GET /api/master-data/teachers` - Danh sách giảng viên

### **Academic**
- `GET /api/academic-years` - Danh sách năm học
- `GET /api/semesters` - Danh sách học kỳ

### **Schedules**
- `GET /api/schedules` - Danh sách lịch giảng
- `POST /api/schedules` - Tạo lịch giảng
- `PUT /api/schedules/:id` - Cập nhật lịch giảng
- `DELETE /api/schedules/:id` - Xóa lịch giảng

### **Teaching Hours**
- `GET /api/teaching-hours/teacher` - Giờ giảng của giảng viên
- `GET /api/teaching-hours/report` - Báo cáo giờ giảng

### **Import/Export**
- `POST /api/import` - Import dữ liệu
- `GET /api/export` - Export dữ liệu
- `GET /api/templates` - Download templates
- `GET /api/import/history` - Lịch sử import

## 🔨 Cách Sử Dụng

### **1. Basic API Call**
```typescript
import { api } from '@/lib/utils/apiClient'
import { API_CONFIG } from '@/lib/config/api'

// GET request
const departments = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS)

// POST request
const newDepartment = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS, {
  name: 'Khoa CNTT',
  code: 'CNTT'
})
```

### **2. With Parameters**
```typescript
// GET with query parameters
const schedules = await api.get(API_CONFIG.ENDPOINTS.SCHEDULES.BASE, {
  page: 1,
  size: 20,
  teacherId: 123
})
```

### **3. File Upload**
```typescript
// Upload file
const result = await api.upload(API_CONFIG.ENDPOINTS.IMPORT_EXPORT.IMPORT, file, {
  type: 'departments'
})
```

### **4. File Download**
```typescript
// Download file
await api.download(API_CONFIG.ENDPOINTS.IMPORT_EXPORT.TEMPLATES, 'departments-template.xlsx')
```

### **5. Error Handling**
```typescript
import { ApiClientError } from '@/lib/utils/apiClient'

try {
  const data = await api.get('/api/some-endpoint')
} catch (error) {
  if (error instanceof ApiClientError) {
    console.error('API Error:', error.status, error.message)
  }
}
```

## 🔐 Authentication

API client tự động thêm Authorization header nếu có token:

```typescript
// Token được lấy từ localStorage
const token = localStorage.getItem('token')
// Headers: { Authorization: `Bearer ${token}` }
```

## 🌍 Environment Setup

### **Development**
```bash
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
```

### **Production**
```bash
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
```

### **Staging**
```bash
NEXT_PUBLIC_API_BASE_URL=https://staging-api.yourdomain.com
```

## 🚨 Error Handling

### **HTTP Status Codes**
- `200-299`: Success
- `400`: Bad Request
- `401`: Unauthorized (redirect to login)
- `403`: Forbidden
- `404`: Not Found
- `408`: Request Timeout
- `500`: Internal Server Error

### **Custom Error Types**
```typescript
export class ApiClientError extends Error {
  public status: number
  public response?: any
}
```

## 📊 Response Format

### **Success Response**
```typescript
interface ApiResponse<T> {
  success: true
  message: string
  data: T
  metadata?: any
}
```

### **Error Response**
```typescript
interface ApiError {
  success: false
  message: string
  error?: string
  details?: any
}
```

## 🔄 Migration Guide

### **From Old Utils to New API Client**

**Before:**
```typescript
import { apiCall } from '@/lib/utils'
const data = await apiCall('/api/endpoint')
```

**After:**
```typescript
import { api } from '@/lib/utils/apiClient'
import { API_CONFIG } from '@/lib/config/api'
const data = await api.get(API_CONFIG.ENDPOINTS.SOME.ENDPOINT)
```

## 📝 Best Practices

1. **Always use API_CONFIG.ENDPOINTS** thay vì hardcode URLs
2. **Handle errors properly** với try-catch
3. **Use TypeScript types** cho response data
4. **Set appropriate timeouts** cho các request lâu
5. **Use environment variables** cho configuration

---

**🔧 Cấu hình hoàn tất! API client đã sẵn sàng sử dụng.**
